from manim import *
import os
from layout_manager import LayoutManager

class SiLingxiaControversy(Scene):
    def construct(self):
        # 设置背景色为白色
        self.camera.background_color = "#FFFFFF"
        
        # 第一阶段 (0s-3s): 介绍司玲霞商业活动争议
        # 创建人物剪影图标
        person_icon = SVGMobject(
            """
            <svg width="100" height="100" xmlns="http://www.w3.org/2000/svg">
              <circle cx="50" cy="35" r="20" fill="black"/>
              <path d="M30,60 Q50,90 70,60 Z" fill="black"/>
            </svg>
            """
        )
        person_icon.height = 2
        person_icon.set_opacity(0)
        
        # 创建标题文字
        title = Text("司玲霞商业活动争议", font="SimHei", font_size=36, color=RED)
        title.move_to([0, 3, 0])
        title.set_opacity(0)
        
        # 创建放射线条和圆点
        lines = VGroup()
        dots = VGroup()
        
        for angle in range(0, 360, 60):
            line = Line(
                start=ORIGIN,
                end=1.5 * (np.cos(angle * DEGREES), np.sin(angle * DEGREES), 0),
                stroke_width=2,
                color=GRAY
            )
            dot = Dot(
                point=1.5 * (np.cos(angle * DEGREES), np.sin(angle * DEGREES), 0),
                radius=0.1,
                color=BLUE
            )
            lines.add(line)
            dots.add(dot)
        
        lines.set_opacity(0)
        dots.set_opacity(0)
        
        # 播放第一阶段动画
        self.play(
            FadeIn(person_icon),
            run_time=1
        )
        self.play(
            Write(title),
            run_time=1
        )
        self.play(
            FadeIn(lines),
            run_time=0.5
        )
        self.play(
            FadeIn(dots),
            run_time=0.5
        )
        
        # 第二阶段 (4s-8s): 任期重合
        # 创建组织关系图
        org_chart = VGroup()
        
        # 司玲霞图标
        si_icon = person_icon.copy()
        si_icon.scale(0.8)
        si_icon.move_to([-2, 1, 0])
        
        # 杨伟图标 (带公文包)
        yang_icon = VGroup(
            SVGMobject(
                """
                <svg width="100" height="100" xmlns="http://www.w3.org/2000/svg">
                  <circle cx="50" cy="35" r="20" fill="black"/>
                  <path d="M30,60 Q50,90 70,60 Z" fill="black"/>
                </svg>
                """
            ),
            SVGMobject(
                """
                <svg width="100" height="100" xmlns="http://www.w3.org/2000/svg">
                  <rect x="30" y="60" width="40" height="30" fill="black"/>
                  <rect x="45" y="50" width="10" height="10" fill="black"/>
                </svg>
                """
            )
        )
        yang_icon.scale(0.8)
        yang_icon.move_to([2, 1, 0])
        
        # 连接线
        connection = Line(si_icon.get_center(), yang_icon.get_center(), color=GRAY)
        
        # 组合组织图
        org_chart.add(si_icon, yang_icon, connection)
        org_chart.set_opacity(0)
        
        # 创建时间轴
        timeline = VGroup()
        
        # 时间轴基线
        base_line = Line([-4, -2, 0], [4, -2, 0], color=GRAY)
        
        # 司玲霞创业时间线
        si_timeline = Line([-3, -2, 0], [2, -2, 0], color=GREEN, stroke_width=4)
        
        # 杨伟任期时间线
        yang_timeline = Line([-2, -1.7, 0], [3, -1.7, 0], color=BLUE, stroke_width=4)
        
        # 重叠部分高亮
        overlap = Rectangle(
            width=4,
            height=0.5,
            fill_color=YELLOW,
            fill_opacity=0.3,
            stroke_opacity=0
        )
        overlap.move_to([0, -1.85, 0])
        
        # 组合时间轴
        timeline.add(base_line, si_timeline, yang_timeline, overlap)
        timeline.set_opacity(0)
        
        # 创建关键词和说明文字
        overlap_keyword = Text("任期重合", font="SimHei", font_size=32, color=BLUE)
        overlap_keyword.move_to([3, 3, 0])
        overlap_keyword.set_opacity(0)
        
        overlap_desc = Text("创办公司时间与丈夫杨伟公职任期高度重合", font="SimHei", font_size=24)
        overlap_desc.move_to([0, -3, 0])
        overlap_desc.set_opacity(0)
        
        # 播放第二阶段动画
        self.play(
            FadeOut(person_icon),
            FadeOut(lines),
            FadeOut(dots),
            FadeIn(org_chart),
            run_time=1
        )
        self.play(
            FadeIn(overlap_keyword),
            run_time=0.5
        )
        self.play(
            FadeIn(timeline),
            run_time=1
        )
        
        # 打字机效果显示说明文字
        for i in range(len(overlap_desc.text)):
            self.play(
                overlap_desc[i].animate.set_opacity(1),
                run_time=0.05
            )
        
        # 重叠部分闪烁效果
        self.play(
            overlap.animate.set_opacity(0.7),
            run_time=0.5
        )
        self.play(
            overlap.animate.set_opacity(0.3),
            run_time=0.5
        )
        
        # 第三阶段 (9s-13s): 公司业务问题
        # 创建公司业务示意图
        business_chart = VGroup()
        
        # 公文包图标代表公司
        company_icon = SVGMobject(
            """
            <svg width="100" height="100" xmlns="http://www.w3.org/2000/svg">
              <rect x="20" y="30" width="60" height="40" fill="black"/>
              <rect x="45" y="20" width="10" height="10" fill="black"/>
            </svg>
            """
        )
        company_icon.move_to([0, 0, 0])
        
        # 左侧分支 - 移民咨询
        left_branch = Line([0, 0, 0], [-3, 2, 0], color=GREEN, stroke_width=3)
        left_icon = SVGMobject(
            """
            <svg width="100" height="100" xmlns="http://www.w3.org/2000/svg">
              <circle cx="50" cy="50" r="30" fill="green" fill-opacity="0.3"/>
              <text x="50" y="55" font-size="20" text-anchor="middle" fill="green">✓</text>
            </svg>
            """
        )
        left_icon.height = 1
        left_icon.move_to([-3, 2, 0])
        
        # 右侧分支 - 非法签证
        right_branch = Line([0, 0, 0], [3, 2, 0], color=RED, stroke_width=3)
        right_icon = SVGMobject(
            """
            <svg width="100" height="100" xmlns="http://www.w3.org/2000/svg">
              <circle cx="50" cy="50" r="30" fill="red" fill-opacity="0.3"/>
              <text x="50" y="55" font-size="20" text-anchor="middle" fill="red">!</text>
            </svg>
            """
        )
        right_icon.height = 1
        right_icon.move_to([3, 2, 0])
        
        # 组合业务图
        business_chart.add(company_icon, left_branch, right_branch, left_icon, right_icon)
        business_chart.set_opacity(0)
        
        # 创建关键词和说明文字
        legal_keyword = Text("移民咨询", font="SimHei", font_size=32, color=GREEN)
        legal_keyword.move_to([-3, 3, 0])
        legal_keyword.set_opacity(0)
        
        illegal_keyword = Text("非法签证", font="SimHei", font_size=32, color=RED)
        illegal_keyword.move_to([3, 3, 0])
        illegal_keyword.set_opacity(0)
        
        business_desc = Text("公司涉及非法签证业务问题", font="SimHei", font_size=24)
        business_desc.move_to([0, -3, 0])
        business_desc.set_opacity(0)
        
        # 播放第三阶段动画
        self.play(
            FadeOut(org_chart),
            FadeOut(timeline),
            FadeOut(overlap_keyword),
            FadeOut(overlap_desc),
            FadeIn(business_chart),
            run_time=1
        )
        self.play(
            FadeIn(legal_keyword),
            run_time=0.5
        )
        self.play(
            FadeIn(illegal_keyword),
            run_time=0.5,
            rate_func=rush_into
        )
        self.play(
            FadeIn(business_desc),
            run_time=1
        )
        
        # 第四阶段 (14s-17s): 人物对比关系
        # 创建人物对比关系图
        comparison_chart = VGroup()
        
        # 司亚蒙图标 (左侧)
        brother_icon = SVGMobject(
            """
            <svg width="100" height="100" xmlns="http://www.w3.org/2000/svg">
              <circle cx="50" cy="35" r="20" fill="black"/>
              <path d="M30,60 Q50,90 70,60 Z" fill="black"/>
            </svg>
            """
        )
        brother_icon.move_to([-3, 0, 0])
        
        # 司玲霞图标 (右侧)
        sister_icon = SVGMobject(
            """
            <svg width="100" height="100" xmlns="http://www.w3.org/2000/svg">
              <circle cx="50" cy="35" r="20" fill="black"/>
              <path d="M30,60 Q50,90 70,60 Z" fill="black"/>
            </svg>
            """
        )
        sister_icon.move_to([3, 0, 0])
        
        # 连接线
        relation_line = Line(brother_icon.get_center(), sister_icon.get_center(), color=GRAY)
        
        # 组合对比图
        comparison_chart.add(brother_icon, sister_icon, relation_line)
        comparison_chart.set_opacity(0)
        
        # 创建关键词和说明文字
        sentenced_keyword = Text("被判刑", font="SimHei", font_size=32, color=RED)
        sentenced_keyword.next_to(brother_icon, DOWN, buff=0.5)
        sentenced_keyword.set_opacity(0)
        
        not_punished_keyword = Text("未被追责", font="SimHei", font_size=32, color="#FFA500")  # 橙色
        not_punished_keyword.next_to(sister_icon, DOWN, buff=0.5)
        not_punished_keyword.set_opacity(0)
        
        comparison_desc = Text("司亚蒙被判刑，司玲霞未被追责", font="SimHei", font_size=24)
        comparison_desc.move_to([0, -3, 0])
        comparison_desc.set_opacity(0)
        
        # 播放第四阶段动画
        self.play(
            FadeOut(business_chart),
            FadeOut(legal_keyword),
            FadeOut(illegal_keyword),
            FadeOut(business_desc),
            FadeIn(comparison_chart),
            run_time=1
        )
        self.play(
            FadeIn(sentenced_keyword),
            run_time=0.5
        )
        self.play(
            FadeIn(not_punished_keyword),
            run_time=0.5
        )
        self.play(
            FadeIn(comparison_desc),
            run_time=1
        )
        
        # 第五阶段 (18s-20s): 特权避责质疑
        # 创建问号图标
        question_mark = Text("?", font_size=120, color=RED)
        question_mark.scale(2)
        question_mark.set_opacity(0)
        
        # 创建关键词和说明文字
        privilege_keyword = Text("特权避责", font="SimHei", font_size=36, color=RED)
        privilege_keyword.set_opacity(0)
        
        conclusion_desc = Text("引发特权避责质疑", font="SimHei", font_size=28, weight=BOLD)
        conclusion_desc.move_to([0, -3, 0])
        conclusion_desc.set_opacity(0)
        
        # 播放第五阶段动画
        self.play(
            FadeOut(comparison_chart),
            FadeOut(sentenced_keyword),
            FadeOut(not_punished_keyword),
            FadeOut(comparison_desc),
            FadeIn(question_mark),
            run_time=0.5
        )
        self.play(
            question_mark.animate.scale(1.2),
            run_time=0.5
        )
        self.play(
            FadeIn(privilege_keyword),
            run_time=0.5
        )
        self.play(
            privilege_keyword.animate.scale(1.1),
            run_time=0.25
        )
        self.play(
            privilege_keyword.animate.scale(1/1.1),
            run_time=0.25
        )
        self.play(
            FadeIn(conclusion_desc),
            run_time=0.5
        )
        
        # 确保所有元素在屏幕边界内
        LayoutManager.ensure_screen_bounds(question_mark)
        LayoutManager.ensure_screen_bounds(privilege_keyword)
        LayoutManager.ensure_screen_bounds(conclusion_desc)
        
        # 最终停留
        self.wait(1)