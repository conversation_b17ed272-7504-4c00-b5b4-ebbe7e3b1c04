from manim import *
import os
from layout_manager import LayoutManager

class HuangYangDianTianInvestigation(Scene):
    def construct(self):
        # 设置背景色为白色
        self.camera.background_color = "#FFFFFF"
        
        # 第一阶段 (0s-3s): 展示年龄与作品数量对比
        # 创建文字元素
        title_text = Text("职业影响", font_size=60, color="#000000", font="SimHei")
        title_text.move_to([0, 3.5, 0])
        title_text.set_opacity(0)
        
        age_text = Text("17岁", font_size=72, color="#0000FF", font="SimHei", weight=BOLD)
        age_text.move_to([-3.5, 0, 0])
        age_text.set_opacity(0)
        
        works_text = Text("33部", font_size=72, color="#FF0000", font="SimHei", weight=BOLD)
        works_text.move_to([3.5, 0, 0])
        works_text.set_opacity(0)
        
        # 创建垂直分隔线
        separator = Line(start=[0, 1.5, 0], end=[0, -1.5, 0], color=GRAY)
        separator.set_opacity(0)
        
        # 确保元素不重叠
        LayoutManager.print_layout_debug(title_text, "职业影响")
        LayoutManager.print_layout_debug(age_text, "17岁")
        LayoutManager.print_layout_debug(works_text, "33部")
        LayoutManager.ensure_screen_bounds(title_text)
        LayoutManager.ensure_screen_bounds(age_text)
        LayoutManager.ensure_screen_bounds(works_text)
        
        # 动画：标题滑入，数字放大效果
        self.play(
            FadeIn(title_text, shift=DOWN),
            run_time=0.8
        )
        
        self.play(
            FadeIn(age_text, scale=1.5),
            run_time=0.7
        )
        
        self.play(
            FadeIn(works_text, scale=1.5),
            FadeIn(separator),
            run_time=0.7
        )
        
        self.wait(0.8)
        
        # 第二阶段 (4s-8s): 父亲辞职与女儿出道时间线
        # 创建工作室图片
        studio_image_path = "image.prop"
        if not os.path.exists(studio_image_path):
            print(f"警告: 文件 {studio_image_path} 不存在")
            studio_image = Rectangle(width=6, height=3.4, color=GRAY, fill_opacity=0.3)
            studio_image.add(Text("工作室图片", font="SimHei", color=BLACK))
        else:
            studio_image = ImageMobject(studio_image_path)
            studio_image.height = 3.4
            studio_image.width = 6
        
        studio_image.move_to([3, 0, 0])
        studio_image.set_opacity(0)
        
        # 创建时间线文字
        father_resign_text = Text("父亲辞职", font_size=36, color="#000000", font="SimHei")
        father_resign_text.move_to([-5, 3, 0])
        father_resign_text.set_opacity(0)
        
        studio_create_text = Text("创立工作室", font_size=36, color="#000000", font="SimHei")
        studio_create_text.move_to([-4, 0, 0])
        studio_create_text.set_opacity(0)
        
        daughter_debut_text = Text("女儿出道", font_size=36, color="#000000", font="SimHei")
        daughter_debut_text.move_to([-3, -3, 0])
        daughter_debut_text.set_opacity(0)
        
        overlap_text = Text("高度重合", font_size=42, color="#FF0000", font="SimHei", weight=BOLD)
        overlap_text.move_to([0, -3.5, 0])
        overlap_text.set_opacity(0)
        
        # 创建时间线连接线
        line1 = DashedLine(start=[-5, 2.5, 0], end=[-4, 0.5, 0], color=GRAY)
        line2 = DashedLine(start=[-4, -0.5, 0], end=[-3, -2.5, 0], color=GRAY)
        line1.set_opacity(0)
        line2.set_opacity(0)
        
        # 确保元素不重叠
        LayoutManager.print_layout_debug(studio_image, "工作室图片")
        LayoutManager.print_layout_debug(father_resign_text, "父亲辞职")
        LayoutManager.print_layout_debug(studio_create_text, "创立工作室")
        LayoutManager.print_layout_debug(daughter_debut_text, "女儿出道")
        LayoutManager.print_layout_debug(overlap_text, "高度重合")
        LayoutManager.ensure_screen_bounds(studio_image)
        LayoutManager.ensure_screen_bounds(father_resign_text)
        LayoutManager.ensure_screen_bounds(studio_create_text)
        LayoutManager.ensure_screen_bounds(daughter_debut_text)
        LayoutManager.ensure_screen_bounds(overlap_text)
        
        # 动画：淡出第一阶段元素，淡入第二阶段元素
        self.play(
            FadeOut(title_text),
            FadeOut(age_text),
            FadeOut(works_text),
            FadeOut(separator),
            FadeIn(studio_image),
            run_time=1
        )
        
        # 时间线动画
        self.play(
            FadeIn(father_resign_text),
            run_time=0.7
        )
        
        self.play(
            FadeIn(line1),
            FadeIn(studio_create_text),
            run_time=0.7
        )
        
        self.play(
            FadeIn(line2),
            FadeIn(daughter_debut_text),
            run_time=0.7
        )
        
        # 高度重合文字闪烁效果
        self.play(
            FadeIn(overlap_text),
            run_time=0.5
        )
        
        self.play(
            overlap_text.animate.scale(1.1),
            run_time=0.3
        )
        
        self.play(
            overlap_text.animate.scale(1/1.1),
            run_time=0.3
        )
        
        self.wait(0.8)
        
        # 第三阶段 (9s-12s): 质疑公职人脉为女儿铺路
        # 创建质疑相关文字
        question_text = Text("质疑", font_size=48, color="#000000", font="SimHei", weight=BOLD)
        question_text.move_to([0, 3.5, 0])
        question_text.set_opacity(0)
        
        official_text = Text("公职人脉", font_size=42, color="#0000FF", font="SimHei")
        official_text.move_to([-3, 0, 0])
        official_text.set_opacity(0)
        
        pave_way_text = Text("为女儿铺路", font_size=42, color="#FF0000", font="SimHei")
        pave_way_text.move_to([3, 0, 0])
        pave_way_text.set_opacity(0)
        
        # 创建问号图形
        question_mark = Text("?", font_size=120, color=GRAY_D)
        question_mark.move_to([0, 0, 0])
        question_mark.set_opacity(0)
        
        # 确保元素不重叠
        LayoutManager.print_layout_debug(question_text, "质疑")
        LayoutManager.print_layout_debug(official_text, "公职人脉")
        LayoutManager.print_layout_debug(pave_way_text, "为女儿铺路")
        LayoutManager.ensure_screen_bounds(question_text)
        LayoutManager.ensure_screen_bounds(official_text)
        LayoutManager.ensure_screen_bounds(pave_way_text)
        
        # 动画：淡出第二阶段元素，淡入第三阶段元素
        self.play(
            FadeOut(father_resign_text),
            FadeOut(studio_create_text),
            FadeOut(daughter_debut_text),
            FadeOut(line1),
            FadeOut(line2),
            FadeOut(overlap_text),
            studio_image.animate.set_opacity(0.2),  # 工作室图片变为半透明背景
            run_time=1
        )
        
        # 问号和质疑文字动画
        self.play(
            FadeIn(question_mark, scale=0.5),
            run_time=0.7
        )
        
        self.play(
            FadeIn(question_text, shift=DOWN),
            run_time=0.7
        )
        
        # 公职人脉和为女儿铺路文字以打字机效果显示
        for text, position in [(official_text, [-3, 0, 0]), (pave_way_text, [3, 0, 0])]:
            text_characters = []
            for i, char in enumerate(text.original_text):
                char_text = Text(char, font_size=42, color=text.color, font="SimHei")
                char_text.move_to([position[0] - len(text.original_text)*0.2/2 + i*0.2, position[1], 0])
                char_text.set_opacity(0)
                text_characters.append(char_text)
            
            for char_text in text_characters:
                self.play(
                    FadeIn(char_text),
                    run_time=0.1
                )
        
        self.wait(0.5)
        
        # 第四阶段 (13s-17s): 影响演艺事业和经纪公司回应
        # 创建声明文件图片
        statement_image_path = "image.prop"
        if not os.path.exists(statement_image_path):
            print(f"警告: 文件 {statement_image_path} 不存在")
            statement_image = Rectangle(width=4, height=6, color=GRAY, fill_opacity=0.3)
            statement_image.add(Text("声明文件", font="SimHei", color=BLACK))
        else:
            statement_image = ImageMobject(statement_image_path)
            statement_image.height = 6
            statement_image.width = 4
        
        statement_image.move_to([3, 0, 0])
        statement_image.set_opacity(0)
        
        # 创建相关文字
        impact_text = Text("严重影响演艺事业", font_size=42, color="#FF0000", font="SimHei")
        impact_text.move_to([-4, 0, 0])
        impact_text.set_opacity(0)
        
        company_text = Text("嘉行传媒", font_size=36, color="#000000", font="SimHei")
        company_text.move_to([3, 3, 0])
        company_text.set_opacity(0)
        
        criticism_text = Text("避重就轻", font_size=36, color="#FF0000", font="SimHei", weight=BOLD)
        criticism_text.move_to([3, -3, 0])
        criticism_text.set_opacity(0)
        
        # 创建虚线框突出显示
        dashed_rect = DashedVMobject(Rectangle(width=3, height=1, color=RED))
        dashed_rect.move_to([3, -3, 0])
        dashed_rect.set_opacity(0)
        
        # 确保元素不重叠
        LayoutManager.print_layout_debug(statement_image, "声明文件")
        LayoutManager.print_layout_debug(impact_text, "严重影响演艺事业")
        LayoutManager.print_layout_debug(company_text, "嘉行传媒")
        LayoutManager.print_layout_debug(criticism_text, "避重就轻")
        LayoutManager.ensure_screen_bounds(statement_image)
        LayoutManager.ensure_screen_bounds(impact_text)
        LayoutManager.ensure_screen_bounds(company_text)
        LayoutManager.ensure_screen_bounds(criticism_text)
        
        # 动画：淡出第三阶段元素，淡入第四阶段元素
        self.play(
            FadeOut(question_mark),
            FadeOut(question_text),
            FadeOut(official_text),
            FadeOut(pave_way_text),
            FadeOut(studio_image),
            run_time=1
        )
        
        # 放大镜效果展示声明文件
        magnifier = Circle(radius=2, color=GRAY)
        magnifier.set_fill(opacity=0.1)
        magnifier.move_to([6, 0, 0])
        
        self.play(
            FadeIn(magnifier),
            run_time=0.5
        )
        
        self.play(
            magnifier.animate.move_to([3, 0, 0]),
            FadeIn(statement_image),
            run_time=1
        )
        
        self.play(
            FadeOut(magnifier),
            run_time=0.5
        )
        
        # 显示相关文字
        self.play(
            FadeIn(impact_text),
            run_time=0.7
        )
        
        # 波浪动效
        self.play(
            impact_text.animate.shift(UP*0.2),
            run_time=0.3
        )
        
        self.play(
            impact_text.animate.shift(DOWN*0.4),
            run_time=0.3
        )
        
        self.play(
            impact_text.animate.shift(UP*0.2),
            run_time=0.3
        )
        
        self.play(
            FadeIn(company_text),
            run_time=0.7
        )
        
        # 避重就轻文字闪烁效果
        self.play(
            FadeIn(criticism_text),
            FadeIn(dashed_rect),
            run_time=0.7
        )
        
        self.play(
            criticism_text.animate.set_opacity(0.5),
            run_time=0.3
        )
        
        self.play(
            criticism_text.animate.set_opacity(1),
            run_time=0.3
        )
        
        self.wait(1)