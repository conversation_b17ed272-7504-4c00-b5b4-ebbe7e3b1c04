from manim import *
import os
from layout_manager import LayoutManager

class YaanInvestigationReport(Scene):
    def construct(self):
        # 设置背景色为白色
        self.camera.background_color = "#FFFFFF"
        
        # ===== 第一阶段 (0s-3s): 雅安市联合工作组调查通报 =====
        # 创建公文图标（使用几何图形代替，因为没有提供具体资产路径）
        document_icon = SVGMobject(
            """
            <svg viewBox="0 0 100 100">
                <rect x="20" y="10" width="60" height="80" fill="white" stroke="red" stroke-width="3"/>
                <line x1="30" y1="30" x2="70" y2="30" stroke="red" stroke-width="2"/>
                <line x1="30" y1="40" x2="70" y2="40" stroke="red" stroke-width="2"/>
                <line x1="30" y1="50" x2="70" y2="50" stroke="red" stroke-width="2"/>
                <line x1="30" y1="60" x2="70" y2="60" stroke="red" stroke-width="2"/>
                <circle cx="65" cy="75" r="15" fill="none" stroke="red" stroke-width="3"/>
            </svg>
            """
        )
        document_icon.height = 2
        document_icon.set_opacity(0)
        
        # 创建标题和副标题
        title = Text("雅安市调查通报核查", font="SimHei", font_size=36, color=BLACK, weight=BOLD)
        title.set_opacity(0)
        title.move_to([0, 2, 0])
        
        subtitle = Text("雅安市联合工作组调查通报", font="SimHei", font_size=24, color="#777777")
        subtitle.set_opacity(0)
        subtitle.move_to([0, 1, 0])
        
        # 确保布局安全
        LayoutManager.ensure_screen_bounds(title)
        LayoutManager.ensure_screen_bounds(subtitle)
        LayoutManager.ensure_screen_bounds(document_icon)
        
        # 动画：公文图标从中心向外扩散出现，标题弹出，副标题淡入
        self.play(
            FadeIn(document_icon, scale=1.5),
            run_time=0.8
        )
        self.play(
            Transform(document_icon, document_icon.copy().scale(0.8).move_to([0, 0, 0])),
            run_time=0.5
        )
        self.play(
            Write(title),
            run_time=0.8
        )
        self.play(
            FadeIn(subtitle),
            run_time=0.5
        )
        self.wait(0.4)
        
        # ===== 第二阶段 (3s-7s): 已确认问题 =====
        # 创建标题和图标
        confirmed_title = Text("已确认问题", font="SimHei", font_size=30, color="#00008B", weight=BOLD)
        confirmed_title.move_to([-4, 2, 0])
        confirmed_title.set_opacity(0)
        
        # 创建商业活动图标（使用几何图形代替）
        business_icon = SVGMobject(
            """
            <svg viewBox="0 0 100 100">
                <rect x="20" y="40" width="60" height="50" fill="white" stroke="black" stroke-width="3"/>
                <polygon points="20,40 50,10 80,40" fill="white" stroke="black" stroke-width="3"/>
                <rect x="40" y="60" width="20" height="30" fill="white" stroke="black" stroke-width="2"/>
                <circle cx="50" cy="75" r="3" fill="black"/>
            </svg>
            """
        )
        business_icon.height = 2
        business_icon.move_to([-3, 1, 0])
        business_icon.set_opacity(0)
        
        # 创建家庭/孩子图标（使用几何图形代替）
        family_icon = SVGMobject(
            """
            <svg viewBox="0 0 100 100">
                <circle cx="50" cy="30" r="15" fill="white" stroke="black" stroke-width="3"/>
                <circle cx="30" cy="40" r="10" fill="white" stroke="black" stroke-width="3"/>
                <circle cx="70" cy="40" r="10" fill="white" stroke="black" stroke-width="3"/>
                <line x1="50" y1="45" x2="50" y2="70" stroke="black" stroke-width="3"/>
                <line x1="30" y1="50" x2="30" y2="70" stroke="black" stroke-width="3"/>
                <line x1="70" y1="50" x2="70" y2="70" stroke="black" stroke-width="3"/>
                <line x1="50" y1="55" x2="30" y2="55" stroke="black" stroke-width="3"/>
                <line x1="50" y1="55" x2="70" y2="55" stroke="black" stroke-width="3"/>
            </svg>
            """
        )
        family_icon.height = 2
        family_icon.move_to([3, 1, 0])
        family_icon.set_opacity(0)
        
        # 创建问题文本
        business_text = Text("✓ 违规经商办企业", font="SimHei", font_size=28, color="#333333")
        business_text.move_to([-3, -1, 0])
        business_text.set_opacity(0)
        
        family_text = Text("✓ 隐瞒违法生育二孩", font="SimHei", font_size=28, color="#333333")
        family_text.move_to([3, -1, 0])
        family_text.set_opacity(0)
        
        # 确保布局安全
        LayoutManager.ensure_screen_bounds(confirmed_title)
        LayoutManager.ensure_screen_bounds(business_icon)
        LayoutManager.ensure_screen_bounds(family_icon)
        LayoutManager.ensure_screen_bounds(business_text)
        LayoutManager.ensure_screen_bounds(family_text)
        
        # 动画：淡出第一阶段元素，淡入第二阶段元素
        self.play(
            FadeOut(document_icon),
            FadeOut(title),
            FadeOut(subtitle),
            run_time=0.5
        )
        
        self.play(
            FadeIn(confirmed_title),
            run_time=0.5
        )
        
        # 左侧商业问题动画
        self.play(
            FadeIn(business_icon),
            run_time=0.5
        )
        self.play(
            FadeIn(business_text),
            run_time=0.5
        )
        self.play(
            business_text.animate.scale(1.1),
            run_time=0.3
        )
        self.play(
            business_text.animate.scale(1/1.1),
            run_time=0.2
        )
        
        # 右侧家庭问题动画
        self.play(
            FadeIn(family_icon),
            run_time=0.5
        )
        self.play(
            FadeIn(family_text),
            run_time=0.5
        )
        self.play(
            family_text.animate.scale(1.1),
            run_time=0.3
        )
        self.play(
            family_text.animate.scale(1/1.1),
            run_time=0.2
        )
        
        self.wait(0.5)
        
        # ===== 第三阶段 (7s-11s): 调查认定 =====
        # 创建标题和图标
        investigation_title = Text("调查认定", font="SimHei", font_size=30, color="#00008B", weight=BOLD)
        investigation_title.move_to([0, 2, 0])
        investigation_title.set_opacity(0)
        
        # 创建公司/商业相关图标（使用几何图形代替）
        company_icon = SVGMobject(
            """
            <svg viewBox="0 0 100 100">
                <rect x="10" y="30" width="80" height="60" fill="white" stroke="black" stroke-width="3"/>
                <rect x="20" y="40" width="15" height="15" fill="white" stroke="black" stroke-width="2"/>
                <rect x="45" y="40" width="15" height="15" fill="white" stroke="black" stroke-width="2"/>
                <rect x="70" y="40" width="15" height="15" fill="white" stroke="black" stroke-width="2"/>
                <rect x="20" y="65" width="15" height="15" fill="white" stroke="black" stroke-width="2"/>
                <rect x="45" y="65" width="15" height="15" fill="white" stroke="black" stroke-width="2"/>
                <rect x="70" y="65" width="15" height="15" fill="white" stroke="black" stroke-width="2"/>
                <line x1="40" y1="20" x2="60" y2="20" stroke="black" stroke-width="3"/>
                <line x1="50" y1="10" x2="50" y2="30" stroke="black" stroke-width="3"/>
            </svg>
            """
        )
        company_icon.height = 3
        company_icon.width = 4
        company_icon.move_to([0, 0, 0])
        company_icon.set_opacity(0)
        
        # 创建认定文本
        company_text = Text("妻子司玲霞经营的相关公司按规定运行", font="SimHei", font_size=28, color="#333333")
        company_text.move_to([0, -2, 0])
        company_text.set_opacity(0)
        
        # 确保布局安全
        LayoutManager.ensure_screen_bounds(investigation_title)
        LayoutManager.ensure_screen_bounds(company_icon)
        LayoutManager.ensure_screen_bounds(company_text)
        
        # 动画：淡出第二阶段元素，淡入第三阶段元素
        self.play(
            FadeOut(confirmed_title),
            FadeOut(business_icon),
            FadeOut(family_icon),
            FadeOut(business_text),
            FadeOut(family_text),
            run_time=0.5
        )
        
        self.play(
            FadeIn(investigation_title),
            run_time=0.5
        )
        
        self.play(
            FadeIn(company_icon),
            run_time=0.7
        )
        
        # 打字机效果显示文本
        for i in range(len(company_text.submobjects)):
            self.play(
                FadeIn(company_text.submobjects[i]),
                run_time=0.05
            )
        
        # 高亮"按规定运行"
        highlight_text = Text("按规定运行", font="SimHei", font_size=28, color=BLUE)
        highlight_text.move_to(company_text.get_center() + RIGHT * 1.5)
        
        self.play(
            FadeIn(highlight_text),
            run_time=0.3
        )
        self.play(
            highlight_text.animate.scale(1.2),
            run_time=0.3
        )
        self.play(
            FadeOut(highlight_text),
            run_time=0.3
        )
        
        self.wait(0.5)
        
        # ===== 第四阶段 (11s-16s): 未回应问题 =====
        # 创建标题
        unanswered_title = Text("未回应问题", font="SimHei", font_size=30, color=RED, weight=BOLD)
        unanswered_title.move_to([0, 2, 0])
        unanswered_title.set_opacity(0)
        
        # 创建问号图标（使用几何图形代替）
        question_mark = Text("?", font_size=36, color=RED, weight=BOLD)
        question_mark.set_opacity(0)
        
        # 创建耳环图标（使用几何图形代替）
        earring_icon = SVGMobject(
            """
            <svg viewBox="0 0 100 100">
                <circle cx="50" cy="30" r="10" fill="white" stroke="black" stroke-width="3"/>
                <path d="M50,40 Q50,70 40,80" fill="none" stroke="black" stroke-width="3"/>
                <circle cx="40" cy="80" r="5" fill="white" stroke="black" stroke-width="2"/>
            </svg>
            """
        )
        earring_icon.height = 1.5
        earring_icon.move_to([-4, -2, 0])
        earring_icon.set_opacity(0)
        
        # 创建金钱/财富图标（使用几何图形代替）
        money_icon = SVGMobject(
            """
            <svg viewBox="0 0 100 100">
                <circle cx="50" cy="50" r="30" fill="white" stroke="black" stroke-width="3"/>
                <text x="50" y="50" font-family="Arial" font-size="40" text-anchor="middle" dominant-baseline="middle" fill="black">$</text>
            </svg>
            """
        )
        money_icon.height = 1.5
        money_icon.move_to([0, -2, 0])
        money_icon.set_opacity(0)
        
        # 创建问题文本
        earring_text = Text("? 耳环真伪", font="SimHei", font_size=28, color=RED)
        earring_text.move_to([-4, 0, 0])
        earring_text.set_opacity(0)
        
        money_text = Text("? 巨额资产来源", font="SimHei", font_size=28, color=RED)
        money_text.move_to([0, 0, 0])
        money_text.set_opacity(0)
        
        other_text = Text("? 其他疑点", font="SimHei", font_size=28, color=RED)
        other_text.move_to([4, 0, 0])
        other_text.set_opacity(0)
        
        # 确保布局安全
        LayoutManager.ensure_screen_bounds(unanswered_title)
        LayoutManager.ensure_screen_bounds(earring_icon)
        LayoutManager.ensure_screen_bounds(money_icon)
        LayoutManager.ensure_screen_bounds(earring_text)
        LayoutManager.ensure_screen_bounds(money_text)
        LayoutManager.ensure_screen_bounds(other_text)
        
        # 动画：淡出第三阶段元素，淡入第四阶段元素
        self.play(
            FadeOut(investigation_title),
            FadeOut(company_icon),
            FadeOut(company_text),
            run_time=0.5
        )
        
        self.play(
            FadeIn(unanswered_title),
            run_time=0.5
        )
        
        # 显示三个问题点
        self.play(
            FadeIn(earring_text),
            FadeIn(earring_icon),
            run_time=0.7
        )
        
        self.play(
            FadeIn(money_text),
            FadeIn(money_icon),
            run_time=0.7
        )
        
        self.play(
            FadeIn(other_text),
            run_time=0.7
        )
        
        # 强调动画
        self.play(
            earring_text.animate.scale(1.1),
            money_text.animate.scale(1.1),
            other_text.animate.scale(1.1),
            run_time=0.4
        )
        self.play(
            earring_text.animate.scale(1/1.1),
            money_text.animate.scale(1/1.1),
            other_text.animate.scale(1/1.1),
            run_time=0.3
        )
        
        self.wait(0.5)
        
        # ===== 第五阶段 (16s-17s): 结论 =====
        # 创建结论文本
        conclusion_text = Text("通报被批评'避重就轻'", font="SimHei", font_size=36, color=BLACK, weight=BOLD)
        conclusion_text.move_to([0, 0, 0])
        conclusion_text.set_opacity(0)
        
        # 确保布局安全
        LayoutManager.ensure_screen_bounds(conclusion_text)
        
        # 动画：淡出第四阶段元素，淡入结论
        self.play(
            FadeOut(unanswered_title),
            FadeOut(earring_text),
            FadeOut(money_text),
            FadeOut(other_text),
            FadeOut(earring_icon),
            FadeOut(money_icon),
            run_time=0.3
        )
        
        self.play(
            FadeIn(conclusion_text),
            run_time=0.3
        )
        
        # 强调"避重就轻"
        highlight_conclusion = Text("'避重就轻'", font="SimHei", font_size=40, color=RED, weight=BOLD)
        highlight_conclusion.move_to(conclusion_text.get_center() + DOWN * 1)
        
        self.play(
            FadeIn(highlight_conclusion),
            run_time=0.2
        )
        self.play(
            highlight_conclusion.animate.scale(1.2),
            run_time=0.2
        )
        
        # 淡出结束
        self.play(
            FadeOut(conclusion_text),
            FadeOut(highlight_conclusion),
            run_time=0.3
        )