from manim import *
import os
from layout_manager import LayoutManager

class FamilyControversyAnimation(Scene):
    def construct(self):
        # 设置背景色
        self.camera.background_color = "#FFFFFF"
        
        # 第一阶段 (0.0s-1.0s): 标题"家族问题"
        title = Text("家族问题", font_size=72, color=WHITE, font="SimHei")
        title.set_opacity(0)
        
        # 创建深蓝色背景
        blue_bg = Rectangle(width=title.width + 1, height=title.height + 0.5, color=BLUE_E, fill_opacity=1)
        blue_bg.set_opacity(0)
        
        # 组合标题和背景
        title_group = VGroup(blue_bg, title)
        
        # 标题从小到大淡入
        self.play(
            FadeIn(blue_bg, scale=1.2),
            FadeIn(title, scale=1.2),
            run_time=0.3
        )
        self.wait(0.7)
        
        # 标题淡出
        self.play(
            FadeOut(title_group),
            run_time=0.5
        )
        self.wait(0.5)
        
        # 第二阶段 (2.0s-5.0s): 司亚蒙违法行为
        # 检查图片路径
        uncle_image_path = "image.character"
        if not os.path.exists(uncle_image_path):
            print(f"警告: 图片文件 {uncle_image_path} 不存在")
            # 创建一个替代图像
            uncle_image = Rectangle(width=3, height=4, color=GRAY, fill_opacity=0.5)
            uncle_image.add(Text("司亚蒙图片", font="SimHei", font_size=24, color=WHITE))
        else:
            uncle_image = ImageMobject(uncle_image_path)
            # 根据宽高比0.75设置尺寸
            uncle_image.height = 4
            uncle_image.width = 4 * 0.75  # 宽高比0.75
        
        uncle_image.move_to([-4, 0, 0])
        uncle_image.set_opacity(0)
        
        # 创建文字元素
        uncle_name = Text("黄杨钿甜舅舅：司亚蒙", font_size=36, color=WHITE, font="SimHei")
        uncle_name.move_to([-4, -2, 0])
        uncle_name.set_opacity(0)
        
        illegal_act1 = Text("协助非法办理港澳通行证", font_size=40, color=RED, font="SimHei", weight=BOLD)
        illegal_act1.move_to([3, 1, 0])
        illegal_act1.set_opacity(0)
        
        or_text = Text("或", font_size=36, color=GRAY_D, font="SimHei")
        or_text.move_to([3, 0, 0])
        or_text.set_opacity(0)
        
        illegal_act2 = Text("走私", font_size=40, color=RED, font="SimHei", weight=BOLD)
        illegal_act2.move_to([3, -0.5, 0])
        illegal_act2.set_opacity(0)
        
        sentence = Text("被判刑", font_size=40, color=RED, font="SimHei", weight=BOLD)
        sentence.move_to([3, -1.5, 0])
        sentence.set_opacity(0)
        
        # 确保元素不重叠
        LayoutManager.ensure_screen_bounds(uncle_image)
        LayoutManager.ensure_screen_bounds(uncle_name)
        LayoutManager.ensure_screen_bounds(illegal_act1)
        LayoutManager.ensure_screen_bounds(or_text)
        LayoutManager.ensure_screen_bounds(illegal_act2)
        LayoutManager.ensure_screen_bounds(sentence)
        
        # 显示司亚蒙照片和名称
        self.play(
            FadeIn(uncle_image),
            FadeIn(uncle_name),
            run_time=0.5
        )
        
        # 轻微放大效果
        self.play(
            uncle_image.animate.scale(1.05),
            run_time=0.3
        )
        
        # 显示违法行为1
        self.play(
            FadeIn(illegal_act1, scale=1.2),
            run_time=0.3
        )
        
        # 显示"或"
        self.play(
            FadeIn(or_text),
            run_time=0.2
        )
        
        # 显示违法行为2
        self.play(
            FadeIn(illegal_act2, scale=1.2),
            run_time=0.3
        )
        
        # 显示"被判刑"并带有闪动效果
        self.play(
            FadeIn(sentence),
            run_time=0.3
        )
        self.play(
            sentence.animate.scale(1.1),
            run_time=0.1
        )
        self.play(
            sentence.animate.scale(1/1.1),
            run_time=0.1
        )
        
        # 等待一段时间
        self.wait(0.7)
        
        # 清除所有元素
        self.play(
            *[FadeOut(mob) for mob in [uncle_image, uncle_name, illegal_act1, or_text, illegal_act2, sentence]],
            run_time=0.5
        )
        
        # 第三阶段 (6.0s-11.0s): 高消费生活方式
        # 检查图片路径
        bracelet_image_path = "image.prop"
        if not os.path.exists(bracelet_image_path):
            print(f"警告: 图片文件 {bracelet_image_path} 不存在")
            # 创建一个替代图像
            bracelet_image = Rectangle(width=4, height=5, color=GOLD, fill_opacity=0.5)
            bracelet_image.add(Text("卡地亚手镯", font="SimHei", font_size=24, color=WHITE))
        else:
            bracelet_image = ImageMobject(bracelet_image_path)
            # 根据宽高比0.80设置尺寸
            bracelet_image.height = 5
            bracelet_image.width = 5 * 0.80  # 宽高比0.80
        
        bracelet_image.move_to([2, 0, 0])
        bracelet_image.set_opacity(0)
        
        # 创建文字元素
        mother_text = Text("黄杨钿甜母亲(司玲霞)佩戴", font_size=36, color=WHITE, font="SimHei")
        mother_text.move_to([0, 2, 0])
        mother_text.set_opacity(0)
        
        price_text = Text("40-45万元", font_size=48, color=GOLD, font="SimHei")
        price_text.set_stroke(BLACK, width=0.5, background=True)  # 黑色描边
        price_text.move_to([-3, 0, 0])
        price_text.set_opacity(0)
        
        comparison_text = Text("约相当于普通家庭4年收入", font_size=24, color=WHITE, font="SimHei")
        comparison_text.move_to([-3, -1, 0])
        comparison_text.set_opacity(0)
        
        lifestyle_text = Text("高消费生活方式", font_size=36, color=WHITE, font="SimHei")
        lifestyle_text.move_to([4, 1, 0])
        lifestyle_text.set_opacity(0)
        
        question_text = Text("财富来源质疑", font_size=36, color=WHITE, font="SimHei")
        question_text.move_to([4, -1, 0])
        question_text.set_opacity(0)
        
        # 确保元素不重叠
        LayoutManager.ensure_screen_bounds(bracelet_image)
        LayoutManager.ensure_screen_bounds(mother_text)
        LayoutManager.ensure_screen_bounds(price_text)
        LayoutManager.ensure_screen_bounds(comparison_text)
        LayoutManager.ensure_screen_bounds(lifestyle_text)
        LayoutManager.ensure_screen_bounds(question_text)
        
        # 显示手镯图片和母亲文字
        self.play(
            FadeIn(bracelet_image),
            FadeIn(mother_text),
            run_time=0.5
        )
        
        # 慢速缩放效果
        self.play(
            bracelet_image.animate.scale(1.1),
            run_time=1.0
        )
        
        # 显示价格标签
        self.play(
            FadeIn(price_text, scale=1.2),
            run_time=0.5
        )
        
        # 显示对比信息
        self.play(
            FadeIn(comparison_text),
            run_time=0.5
        )
        
        # 显示高消费生活方式
        self.play(
            FadeIn(lifestyle_text),
            run_time=0.5
        )
        
        # 显示财富来源质疑
        self.play(
            FadeIn(question_text),
            run_time=0.5
        )
        
        # 等待一段时间
        self.wait(1.5)
        
        # 清除所有元素
        self.play(
            *[FadeOut(mob) for mob in [bracelet_image, mother_text, price_text, comparison_text, lifestyle_text, question_text]],
            run_time=0.5
        )
        
        # 第四阶段 (12.0s-16.0s): 删除社交媒体内容
        # 检查图片路径
        delete_icon_path = "简化的社交媒体删除内容图标.png"
        if not os.path.exists(delete_icon_path):
            print(f"警告: 图片文件 {delete_icon_path} 不存在")
            # 创建一个替代图像
            delete_icon = Rectangle(width=6, height=3.4, color=LIGHT_GRAY, fill_opacity=0.5)
            
            # 添加X符号
            x_mark = VGroup(
                Line([-1, 1, 0], [1, -1, 0], color=RED, stroke_width=4),
                Line([-1, -1, 0], [1, 1, 0], color=RED, stroke_width=4)
            )
            delete_icon.add(x_mark)
            delete_icon.add(Text("删除的社交媒体内容", font="SimHei", font_size=24, color=WHITE))
        else:
            delete_icon = ImageMobject(delete_icon_path)
            # 根据宽高比1.78设置尺寸
            delete_icon.height = 3.4
            delete_icon.width = 3.4 * 1.78  # 宽高比1.78
        
        delete_icon.move_to([0, 0, 0])
        delete_icon.set_opacity(0)
        
        # 创建文字元素
        delete_text = Text("删除社交媒体炫富内容", font_size=40, color=WHITE, font="SimHei")
        delete_text.set_opacity(0.7)  # 半透明
        delete_text.move_to([0, 1, 0])
        delete_text.set_opacity(0)
        
        hide_text = Text("保持"隐身"状态", font_size=40, color=WHITE, font="SimHei")
        hide_text.set_opacity(0.7)  # 半透明
        hide_text.move_to([0, -1, 0])
        hide_text.set_opacity(0)
        
        controversy_text = Text("争议持续", font_size=36, color=WHITE, font="SimHei")
        controversy_text.move_to([5, -3, 0])
        controversy_text.set_opacity(0)
        
        question_mark = Text("?", font_size=120, color=WHITE, font="SimHei")
        question_mark.move_to([0, 0, 0])
        question_mark.set_opacity(0)
        
        # 确保元素不重叠
        LayoutManager.ensure_screen_bounds(delete_icon)
        LayoutManager.ensure_screen_bounds(delete_text)
        LayoutManager.ensure_screen_bounds(hide_text)
        LayoutManager.ensure_screen_bounds(controversy_text)
        
        # 显示删除图标
        self.play(
            FadeIn(delete_icon),
            run_time=0.5
        )
        
        # 显示删除社交媒体炫富内容
        self.play(
            FadeIn(delete_text),
            run_time=0.5
        )
        
        # 显示保持"隐身"状态
        self.play(
            FadeIn(hide_text),
            run_time=0.5
        )
        
        # "隐身"二字做轻微闪烁效果
        hide_word = Text(""隐身"", font_size=40, color=WHITE, font="SimHei")
        hide_word.set_opacity(0.7)
        hide_word.move_to(hide_text.get_center())
        
        self.play(
            hide_word.animate.scale(1.15),
            run_time=0.2
        )
        self.play(
            hide_word.animate.scale(1/1.15),
            run_time=0.2
        )
        
        # 图标逐渐淡出
        self.play(
            FadeOut(delete_icon),
            FadeOut(delete_text),
            FadeOut(hide_text),
            run_time=0.8
        )
        
        # 显示争议持续
        self.play(
            FadeIn(controversy_text),
            run_time=0.5
        )
        
        # 显示问号
        self.play(
            FadeIn(question_mark),
            run_time=0.7
        )
        
        # 等待一段时间
        self.wait(0.8)
        
        # 整个画面慢慢淡出
        self.play(
            FadeOut(controversy_text),
            FadeOut(question_mark),
            run_time=1.0
        )