from manim import *
import os
from layout_manager import LayoutManager

class HuangYangDiantianConsumptionInvestigation(Scene):
    def construct(self):
        # 设置背景色为白色
        self.camera.background_color = "#FFFFFF"
        
        # 定义资产路径
        character_path = "../data/assets/characters/muqin.jpg"
        prop_path = "../data/assets/props/bieshu.jpg"
        
        # 检查文件是否存在
        if not os.path.exists(character_path):
            print(f"警告: 文件不存在 - {character_path}")
        if not os.path.exists(prop_path):
            print(f"警告: 文件不存在 - {prop_path}")
        
        # 创建人物元素 - 黄杨钿甜的母亲
        try:
            mother_image = ImageMobject(character_path)
            # 设置宽高比 3:4
            mother_image.height = 4
            mother_image.width = 3
            mother_image.set_opacity(0.7)  # 淡化处理效果
        except:
            # 如果图片不存在，创建一个占位符
            mother_image = Rectangle(height=4, width=3, color=GRAY, fill_opacity=0.5)
            mother_image.add(Text("黄杨钿甜母亲", font="SimHei", color=BLACK).scale(0.5))
        
        # 创建道具元素 - 梵克雅宝项链
        try:
            necklace_image = ImageMobject(prop_path)
            # 设置宽高比 4:5
            necklace_image.height = 5
            necklace_image.width = 4
            necklace_image.set_opacity(0)  # 初始透明
        except:
            # 如果图片不存在，创建一个占位符
            necklace_image = Rectangle(height=5, width=4, color=GRAY, fill_opacity=0.5)
            necklace_image.add(Text("梵克雅宝项链", font="SimHei", color=BLACK).scale(0.5))
            necklace_image.set_opacity(0)  # 初始透明
        
        # 创建道具元素 - 深圳别墅
        try:
            villa_image = ImageMobject(prop_path)
            # 设置宽高比 16:9
            villa_image.height = 5.6
            villa_image.width = 10
            villa_image.set_opacity(0)  # 初始透明
        except:
            # 如果图片不存在，创建一个占位符
            villa_image = Rectangle(height=5.6, width=10, color=GRAY, fill_opacity=0.5)
            villa_image.add(Text("深圳别墅", font="SimHei", color=BLACK).scale(0.7))
            villa_image.set_opacity(0)  # 初始透明
        
        # 创建文字元素
        title = Text("奢侈生活", font="SimHei", font_size=72, color=BLACK)
        title.set_opacity(0)  # 初始透明
        
        subtitle = Text("黄杨钿甜家庭消费水平调查", font="SimHei", font_size=36, color=BLACK)
        subtitle.to_edge(UP, buff=0.5)
        subtitle.set_opacity(0)  # 初始透明
        
        year_2016 = Text("2016年", font="SimHei", font_size=36, color=BLACK)
        year_2016.set_opacity(0)  # 初始透明
        
        price_50w = Text("约50万元", font="SimHei", font_size=48, color=WHITE)
        price_50w_bg = BackgroundRectangle(price_50w, color=RED, fill_opacity=1)
        price_50w_group = VGroup(price_50w_bg, price_50w)
        price_50w_group.set_opacity(0)  # 初始透明
        
        necklace_text = Text("梵克雅宝项链", font="SimHei", font_size=36, color=BLACK)
        necklace_text.set_opacity(0)  # 初始透明
        
        yang_wei_note = Text("（杨伟任职期间）", font="SimHei", font_size=28, color=GRAY)
        yang_wei_note.set_opacity(0)  # 初始透明
        
        year_2018 = Text("2018年", font="SimHei", font_size=36, color=BLACK)
        year_2018.set_opacity(0)  # 初始透明
        
        price_40w = Text("40余万元", font="SimHei", font_size=48, color=WHITE)
        price_40w_bg = BackgroundRectangle(price_40w, color=RED, fill_opacity=1)
        price_40w_group = VGroup(price_40w_bg, price_40w)
        price_40w_group.set_opacity(0)  # 初始透明
        
        bracelet_text = Text("卡地亚手镯", font="SimHei", font_size=36, color=BLACK)
        bracelet_text.set_opacity(0)  # 初始透明
        
        villa_text = Text("深圳别墅", font="SimHei", font_size=36, color=BLACK)
        villa_text.set_opacity(0)  # 初始透明
        
        price_billion = Text("价值上亿", font="SimHei", font_size=72, color=WHITE)
        price_billion_bg = BackgroundRectangle(price_billion, color=RED, fill_opacity=1)
        price_billion_group = VGroup(price_billion_bg, price_billion)
        price_billion_group.set_opacity(0)  # 初始透明
        
        conclusion = Text("消费水平与公务员身份极不匹配", font="SimHei", font_size=54, color=BLACK, weight=BOLD)
        conclusion.set_opacity(0)  # 初始透明
        
        # 第一阶段 (0s-3s): 开场介绍
        # 母亲图片从左侧进入
        mother_image.move_to([-4, 0, 0])
        self.add(mother_image)
        mother_image.set_opacity(0)
        
        # 标题从小到大放大出现
        title.scale(0.1)
        self.add(title)
        
        self.play(
            title.animate.scale(10).set_opacity(1),
            run_time=1.5,
            rate_func=smooth
        )
        
        # 副标题从上方滑入
        self.play(
            subtitle.animate.set_opacity(1),
            run_time=0.8,
            rate_func=smooth
        )
        
        # 母亲图片淡入
        self.play(
            mother_image.animate.set_opacity(0.7),
            run_time=0.7,
            rate_func=smooth
        )
        
        # 等待一会
        self.wait(0.5)
        
        # 第二阶段 (4s-8s): 梵克雅宝项链
        # 标题淡出
        self.play(
            title.animate.set_opacity(0),
            run_time=0.5
        )
        
        # 梵克雅宝项链图片出现在右侧
        necklace_image.move_to([3, 0, 0])
        self.add(necklace_image)
        
        # 2016年标签从右上角滑入
        year_2016.move_to([5, 3, 0])
        
        # 梵克雅宝项链文字位置
        necklace_text.move_to([5, 0, 0])
        
        # 价格标签位置
        price_50w_group.move_to([5, -2, 0])
        
        # 杨伟任职期间注释位置
        yang_wei_note.move_to([2, 2, 0])
        
        # 确保元素不重叠
        LayoutManager.ensure_screen_bounds(year_2016)
        LayoutManager.ensure_screen_bounds(necklace_text)
        LayoutManager.ensure_screen_bounds(price_50w_group)
        LayoutManager.ensure_screen_bounds(yang_wei_note)
        
        # 打印布局调试信息
        LayoutManager.print_layout_debug(year_2016, "2016年")
        LayoutManager.print_layout_debug(necklace_text, "梵克雅宝项链")
        LayoutManager.print_layout_debug(price_50w_group, "约50万元")
        LayoutManager.print_layout_debug(yang_wei_note, "杨伟任职期间")
        
        # 动画展示
        self.play(
            necklace_image.animate.set_opacity(1),
            FadeIn(year_2016),
            FadeIn(necklace_text),
            run_time=1
        )
        
        self.play(
            FadeIn(yang_wei_note),
            run_time=0.5
        )
        
        self.play(
            FadeIn(price_50w_group),
            run_time=1,
            rate_func=there_and_back_with_pause
        )
        
        self.play(
            FadeIn(price_50w_group),
            run_time=0.5
        )
        
        # 等待一会
        self.wait(1)
        
        # 第三阶段 (9s-12s): 卡地亚手镯
        # 2018年标签替换2016年
        year_2018.move_to(year_2016.get_center())
        
        # 卡地亚手镯文字位置
        bracelet_text.move_to(necklace_text.get_center())
        
        # 价格标签位置
        price_40w_group.move_to(price_50w_group.get_center())
        
        # 确保元素不重叠
        LayoutManager.ensure_screen_bounds(year_2018)
        LayoutManager.ensure_screen_bounds(bracelet_text)
        LayoutManager.ensure_screen_bounds(price_40w_group)
        
        # 打印布局调试信息
        LayoutManager.print_layout_debug(year_2018, "2018年")
        LayoutManager.print_layout_debug(bracelet_text, "卡地亚手镯")
        LayoutManager.print_layout_debug(price_40w_group, "40余万元")
        
        # 动画展示
        self.play(
            FadeOut(year_2016),
            FadeOut(necklace_text),
            FadeOut(price_50w_group),
            FadeOut(yang_wei_note),
            run_time=0.5
        )
        
        self.play(
            FadeIn(year_2018),
            FadeIn(bracelet_text),
            run_time=1
        )
        
        self.play(
            FadeIn(price_40w_group),
            run_time=1,
            rate_func=there_and_back_with_pause
        )
        
        self.play(
            FadeIn(price_40w_group),
            run_time=0.5
        )
        
        # 等待一会
        self.wait(0.5)
        
        # 第四阶段 (13s-17s): 深圳别墅
        # 清除前面的元素
        self.play(
            FadeOut(mother_image),
            FadeOut(necklace_image),
            FadeOut(year_2018),
            FadeOut(bracelet_text),
            FadeOut(price_40w_group),
            run_time=0.5
        )
        
        # 深圳别墅图片全屏展示
        villa_image.move_to([0, 0, 0])
        self.add(villa_image)
        
        # 深圳别墅文字位置
        villa_text.move_to([-5, 3, 0])
        
        # 价值上亿文字位置
        price_billion_group.move_to([0, 0, 0])
        
        # 结论文字位置
        conclusion.move_to([0, -3, 0])
        
        # 确保元素不重叠
        LayoutManager.ensure_screen_bounds(villa_text)
        LayoutManager.ensure_screen_bounds(price_billion_group)
        LayoutManager.ensure_screen_bounds(conclusion)
        
        # 打印布局调试信息
        LayoutManager.print_layout_debug(villa_text, "深圳别墅")
        LayoutManager.print_layout_debug(price_billion_group, "价值上亿")
        LayoutManager.print_layout_debug(conclusion, "结论")
        
        # 动画展示
        self.play(
            villa_image.animate.scale(0.1).set_opacity(1).scale(10),
            run_time=1,
            rate_func=smooth
        )
        
        self.play(
            FadeIn(villa_text),
            run_time=0.5
        )
        
        self.play(
            FadeIn(price_billion_group),
            run_time=1,
            rate_func=there_and_back_with_pause
        )
        
        self.play(
            FadeIn(price_billion_group),
            run_time=0.5
        )
        
        # 最后3秒，别墅图片淡出，结论文字单独展示
        self.wait(0.5)
        
        self.play(
            FadeOut(villa_image),
            FadeOut(villa_text),
            FadeOut(price_billion_group),
            run_time=0.5
        )
        
        self.play(
            FadeIn(conclusion),
            run_time=1,
            rate_func=smooth
        )
        
        # 等待最后一会
        self.wait(1)