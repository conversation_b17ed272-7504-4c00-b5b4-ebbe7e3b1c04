from manim import *
import os
from layout_manager import LayoutManager

class YangWeiFamilyBusinessNetwork(Scene):
    def construct(self):
        # 设置背景色为白色
        self.camera.background_color = "#FFFFFF"
        
        # 资产路径检查函数
        def check_asset(path, name):
            if not os.path.exists(path):
                print(f"警告: 资产文件 '{path}' ({name}) 不存在!")
                return False
            return True
        
        # 1. 创建主要元素
        
        # 杨伟肖像
        yang_wei_image_path = "杨伟肖像.png"
        if check_asset(yang_wei_image_path, "杨伟肖像"):
            yang_wei_image = ImageMobject(yang_wei_image_path)
            yang_wei_image.height = 2
            yang_wei_image.width = 2  # 宽高比1:1
            yang_wei_image.move_to(ORIGIN)
            yang_wei_image.set_opacity(0)
        else:
            # 如果图片不存在，创建一个占位符
            yang_wei_image = Circle(radius=1, color=BLACK).move_to(ORIGIN)
            yang_wei_image.set_opacity(0)
        
        # 关系网络图背景
        network_bg_path = "关系网络图背景.png"
        if check_asset(network_bg_path, "关系网络图背景"):
            network_bg = ImageMobject(network_bg_path)
            network_bg.height = 6
            network_bg.width = 12  # 宽高比16:9
            network_bg.move_to(ORIGIN)
            network_bg.set_opacity(0)
        else:
            # 如果图片不存在，创建一个占位符
            network_bg = Rectangle(height=6, width=12, color=GREY_E, fill_opacity=0.1).move_to(ORIGIN)
            network_bg.set_opacity(0)
        
        # 深圳市国影影视文化传播有限公司
        guoying_company_path = "深圳市国影影视文化传播有限公司.png"
        if check_asset(guoying_company_path, "深圳市国影影视文化传播有限公司"):
            guoying_company = ImageMobject(guoying_company_path)
            guoying_company.height = 2
            guoying_company.width = 3.5  # 宽高比1.78
            guoying_company.move_to(np.array([-5, 0.5, 0]))
            guoying_company.set_opacity(0)
        else:
            # 如果图片不存在，创建一个占位符
            guoying_company = Rectangle(height=2, width=3.5, color=BLUE_E, fill_opacity=0.5)
            guoying_company.move_to(np.array([-5, 0.5, 0]))
            guoying_company.set_opacity(0)
        
        # 横店黄杨钿甜影视工作室
        studio_path = "横店黄杨钿甜影视工作室.jpg"
        if check_asset(studio_path, "横店黄杨钿甜影视工作室"):
            studio = ImageMobject(studio_path)
            studio.height = 1.7
            studio.width = 3  # 宽高比1.78
            studio.move_to(np.array([5, 0.5, 0]))
            studio.set_opacity(0)
        else:
            # 如果图片不存在，创建一个占位符
            studio = Rectangle(height=1.7, width=3, color=GREEN_E, fill_opacity=0.5)
            studio.move_to(np.array([5, 0.5, 0]))
            studio.set_opacity(0)
        
        # 益善堂（深圳）生物科技有限公司
        biotech_company_path = "益善堂（深圳）生物科技有限公司.png"
        if check_asset(biotech_company_path, "益善堂（深圳）生物科技有限公司"):
            biotech_company = ImageMobject(biotech_company_path)
            biotech_company.height = 2
            biotech_company.width = 3.5  # 宽高比1.78
            biotech_company.move_to(np.array([5, -2.5, 0]))
            biotech_company.set_opacity(0)
        else:
            # 如果图片不存在，创建一个占位符
            biotech_company = Rectangle(height=2, width=3.5, color=YELLOW_E, fill_opacity=0.5)
            biotech_company.move_to(np.array([5, -2.5, 0]))
            biotech_company.set_opacity(0)
        
        # 公司变更前后对比图
        company_change_path = "公司变更前后对比图.png"
        if check_asset(company_change_path, "公司变更前后对比图"):
            company_change = ImageMobject(company_change_path)
            company_change.height = 4
            company_change.width = 8  # 宽高比1.78
            company_change.move_to(np.array([0, -1, 0]))
            company_change.set_opacity(0)
        else:
            # 如果图片不存在，创建一个占位符
            company_change = Rectangle(height=4, width=8, color=RED_E, fill_opacity=0.3)
            company_change.move_to(np.array([0, -1, 0]))
            company_change.set_opacity(0)
        
        # 2. 创建文字元素
        
        # 标题
        title = Text("公司关联：杨伟家族的企业网络", font="SimHei", color=BLACK, font_size=45)
        title.move_to(np.array([0, 3.5, 0]))
        title.set_opacity(0)
        
        # 杨伟名字
        yang_wei_name = Text("杨伟", font="SimHei", color=BLACK, font_size=36)
        yang_wei_name.next_to(yang_wei_image, DOWN, buff=0.2)
        yang_wei_name.set_opacity(0)
        
        # 深圳国影影视文化传播有限公司相关文字
        guoying_name = Text("深圳国影影视文化传播有限公司", font="SimHei", color=BLACK, font_size=30)
        guoying_name.move_to(np.array([-5, 1.5, 0]))
        guoying_name.set_opacity(0)
        
        guoying_year = Text("2014年", font="SimHei", color=RED, font_size=30)
        guoying_year.move_to(np.array([-5, 1, 0]))
        guoying_year.set_opacity(0)
        
        guoying_note = Text("（任职期间）", font="SimHei", color=RED, font_size=30)
        guoying_note.move_to(np.array([-4, 1, 0]))
        guoying_note.set_opacity(0)
        
        guoying_capital = Text("注册资本：500万元", font="SimHei", color=BLACK, font_size=30)
        guoying_capital.move_to(np.array([-5, 0, 0]))
        guoying_capital.set_opacity(0)
        
        # 横店黄杨钿甜影视工作室相关文字
        studio_name = Text("横店黄杨钿甜影视工作室", font="SimHei", color=BLACK, font_size=30)
        studio_name.move_to(np.array([5, 1.5, 0]))
        studio_name.set_opacity(0)
        
        studio_year = Text("2018年", font="SimHei", color=BLACK, font_size=30)
        studio_year.move_to(np.array([5, 1, 0]))
        studio_year.set_opacity(0)
        
        # 益善堂生物科技公司相关文字
        biotech_name = Text("益善堂生物科技公司", font="SimHei", color=BLACK, font_size=30)
        biotech_name.move_to(np.array([5, -2, 0]))
        biotech_name.set_opacity(0)
        
        biotech_year = Text("2020年", font="SimHei", color=BLACK, font_size=30)
        biotech_year.move_to(np.array([5, -2.5, 0]))
        biotech_year.set_opacity(0)
        
        biotech_capital = Text("注册资本：1000万元", font="SimHei", color=YELLOW, font_size=30, weight=BOLD)
        biotech_capital.move_to(np.array([5, -3, 0]))
        biotech_capital.set_opacity(0)
        
        capital_highlight = Text("注册资本高达千万级", font="SimHei", color=YELLOW, font_size=36, weight=BOLD)
        capital_highlight.move_to(np.array([0, -3.5, 0]))
        capital_highlight.set_opacity(0)
        
        # 舆情相关文字
        public_opinion = Text("舆情发酵后", font="SimHei", color=RED, font_size=36)
        public_opinion.move_to(np.array([-5, -1, 0]))
        public_opinion.set_opacity(0)
        
        company_change_text = Text("多家公司紧急变更法人股东", font="SimHei", color=RED, font_size=36)
        company_change_text.move_to(np.array([0, -1, 0]))
        company_change_text.set_opacity(0)
        
        criticism = Text("被质疑为掩盖利益关联", font="SimHei", color=BLACK, font_size=36)
        criticism.move_to(np.array([5, -1, 0]))
        criticism.set_opacity(0)
        
        network_summary = Text("利益关联网络", font="SimHei", color=BLACK, font_size=40)
        network_summary.move_to(np.array([0, -3.5, 0]))
        network_summary.set_opacity(0)
        
        # 3. 创建连接线
        line_to_guoying = Line(yang_wei_image.get_center(), guoying_company.get_center(), color=GREY)
        line_to_guoying.set_opacity(0)
        
        line_to_studio = Line(yang_wei_image.get_center(), studio.get_center(), color=GREY)
        line_to_studio.set_opacity(0)
        
        line_to_biotech = Line(yang_wei_image.get_center(), biotech_company.get_center(), color=GREY)
        line_to_biotech.set_opacity(0)
        
        # 4. 动画实现
        
        # 0.0s ~ 3.0s: 杨伟家族经营多家企业，包括
        self.play(
            FadeIn(network_bg, scale=1.2, rate_func=smooth),
            run_time=1
        )
        
        self.play(
            FadeIn(yang_wei_image, scale=1.2),
            run_time=1
        )
        
        self.play(
            Write(title),
            run_time=1
        )
        
        self.play(
            Write(yang_wei_name),
            run_time=0.5
        )
        
        # 确保布局安全
        LayoutManager.ensure_screen_bounds(yang_wei_image)
        LayoutManager.ensure_screen_bounds(title)
        LayoutManager.ensure_screen_bounds(yang_wei_name)
        LayoutManager.print_layout_debug(yang_wei_image, "杨伟肖像")
        LayoutManager.print_layout_debug(title, "标题")
        
        # 3.0s ~ 7.0s: 二零一四年（任职期间）成立的深圳国影影视文化传播有限公司
        self.play(
            FadeIn(guoying_company),
            Create(line_to_guoying),
            run_time=1
        )
        
        self.play(
            Write(guoying_name),
            run_time=1
        )
        
        self.play(
            Write(guoying_year),
            run_time=0.5
        )
        
        self.play(
            Write(guoying_note),
            Flash(guoying_note, color=RED, flash_radius=0.3),
            run_time=1
        )
        
        self.play(
            Write(guoying_capital),
            run_time=0.5
        )
        
        # 确保布局安全
        LayoutManager.ensure_screen_bounds(guoying_company)
        LayoutManager.ensure_screen_bounds(guoying_name)
        LayoutManager.ensure_screen_bounds(guoying_year)
        LayoutManager.ensure_screen_bounds(guoying_note)
        LayoutManager.ensure_screen_bounds(guoying_capital)
        LayoutManager.print_layout_debug(guoying_company, "深圳国影公司")
        
        # 7.0s ~ 10.0s: 二零一八年成立的横店黄杨钿甜影视工作室
        self.play(
            FadeIn(studio),
            Create(line_to_studio),
            run_time=1
        )
        
        self.play(
            Write(studio_name),
            run_time=1
        )
        
        self.play(
            Write(studio_year),
            run_time=1
        )
        
        # 确保布局安全
        LayoutManager.ensure_screen_bounds(studio)
        LayoutManager.ensure_screen_bounds(studio_name)
        LayoutManager.ensure_screen_bounds(studio_year)
        LayoutManager.print_layout_debug(studio, "横店工作室")
        
        # 10.0s ~ 13.0s: 二零二零年成立的益善堂生物科技公司等，注册资本高达千万级
        self.play(
            FadeIn(biotech_company),
            Create(line_to_biotech),
            run_time=1
        )
        
        self.play(
            Write(biotech_name),
            run_time=0.5
        )
        
        self.play(
            Write(biotech_year),
            run_time=0.5
        )
        
        self.play(
            Write(biotech_capital),
            run_time=0.5
        )
        
        # 数字从0增长到1000的动画效果
        capital_value = Integer(0)
        capital_value.set_color(YELLOW)
        capital_value.scale(1.5)
        capital_value.move_to(np.array([0, -3.5, 0]))
        
        self.play(
            FadeIn(capital_value),
            run_time=0.2
        )
        
        self.play(
            ChangeDecimalToValue(capital_value, 1000),
            run_time=0.8
        )
        
        self.play(
            FadeOut(capital_value),
            FadeIn(capital_highlight),
            run_time=0.5
        )
        
        # 确保布局安全
        LayoutManager.ensure_screen_bounds(biotech_company)
        LayoutManager.ensure_screen_bounds(biotech_name)
        LayoutManager.ensure_screen_bounds(biotech_year)
        LayoutManager.ensure_screen_bounds(biotech_capital)
        LayoutManager.ensure_screen_bounds(capital_highlight)
        LayoutManager.print_layout_debug(biotech_company, "益善堂公司")
        
        # 13.0s ~ 15.0s: 舆情发酵后，多家公司紧急变更法人股东，被质疑为掩盖利益关联
        # 13.0s: 显示"舆情发酵后"
        self.play(
            FadeOut(capital_highlight),
            Write(public_opinion),
            run_time=0.5
        )
        
        # 13.5s: 显示"多家公司紧急变更法人股东"和公司变更对比图
        self.play(
            Write(company_change_text),
            FadeIn(company_change, shift=RIGHT*3),
            run_time=0.5
        )
        
        # 添加红色闪烁边框效果
        guoying_border = SurroundingRectangle(guoying_company, color=RED, buff=0.1)
        biotech_border = SurroundingRectangle(biotech_company, color=RED, buff=0.1)
        
        self.play(
            Create(guoying_border),
            Create(biotech_border),
            run_time=0.5
        )
        
        # 14.0s: 显示"被质疑为掩盖利益关联"
        self.play(
            Write(criticism),
            run_time=0.5
        )
        
        # 14.5s: 视角拉远，显示整个网络
        self.play(
            FadeOut(company_change),
            FadeOut(company_change_text),
            FadeOut(public_opinion),
            FadeOut(criticism),
            FadeIn(network_summary),
            run_time=0.5
        )
        
        # 添加整个网络的红色警示效果
        network_alert = SurroundingRectangle(Group(yang_wei_image, guoying_company, studio, biotech_company), 
                                            color=RED, buff=0.5)
        
        self.play(
            Create(network_alert),
            Flash(network_alert, color=RED, flash_radius=1, line_length=0.1),
            run_time=0.5
        )
        
        # 确保布局安全
        LayoutManager.ensure_screen_bounds(public_opinion)
        LayoutManager.ensure_screen_bounds(company_change_text)
        LayoutManager.ensure_screen_bounds(criticism)
        LayoutManager.ensure_screen_bounds(network_summary)
        LayoutManager.print_layout_debug(company_change, "公司变更对比图")
        
        # 最后停留一下
        self.wait(0.5)