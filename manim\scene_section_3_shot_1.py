from manim import *
import os
from layout_manager import LayoutManager

class YangWeiInvestigation(Scene):
    def construct(self):
        # 设置背景色为白色
        self.camera.background_color = "#FFFFFF"
        
        # 检查资产文件是否存在
        character_path = "../data/assets/characters/character.jpg"
        timeline_path = "时间轴背景图.png"
        network_path = "关系网络图.png"
        stamp_path = "公章效果图.png"
        
        for path in [character_path, timeline_path, network_path, stamp_path]:
            if not os.path.exists(path):
                print(f"警告: 文件 {path} 不存在")
        
        # 创建主要元素
        # 1. 人物照片
        character = ImageMobject(character_path)
        character.height = 4  # 设置高度
        character.width = character.height * 0.75  # 按3:4比例设置宽度
        character.set_opacity(0)  # 初始透明
        
        # 2. 时间轴背景图
        timeline = ImageMobject(timeline_path)
        timeline.height = 2  # 设置高度
        timeline.width = timeline.height * 5.33  # 按16:3比例设置宽度
        timeline.set_opacity(0)  # 初始透明
        
        # 3. 关系网络图
        network = ImageMobject(network_path)
        network.height = 6  # 设置高度
        network.width = network.height * 1.0  # 按1:1比例设置宽度
        network.set_opacity(0)  # 初始透明
        
        # 4. 公章效果图
        stamp = ImageMobject(stamp_path)
        stamp.height = 4  # 设置高度
        stamp.width = stamp.height * 1.0  # 按1:1比例设置宽度
        stamp.set_opacity(0)  # 初始透明
        
        # 创建文字元素
        title = Text("公职问题", font="SimHei", font_size=60, color=BLUE_E)
        title.to_edge(UP, buff=1)
        title.set_opacity(0)
        
        name = Text("杨伟（黄杨钿甜父亲）", font="Microsoft YaHei", font_size=42, color=BLACK)
        name.move_to([-3.5, 1, 0])
        name.set_opacity(0)
        
        text_2010 = Text("2010年 入职雅安市城管局", font="Microsoft YaHei", font_size=36, color=BLACK)
        text_2010.move_to([-4, -1, 0])
        text_2010.set_opacity(0)
        
        text_2014 = Text("2014年 借调投资促进局", font="Microsoft YaHei", font_size=36, color=BLACK)
        text_2014.move_to([0, -1, 0])
        text_2014.set_opacity(0)
        
        text_2017 = Text("2017年 辞职", font="Microsoft YaHei", font_size=36, color=BLACK)
        text_2017.move_to([4, -1, 0])
        text_2017.set_opacity(0)
        
        violation_title = Text("违规经商办企业", font="SimHei", font_size=48, color=RED, weight=BOLD)
        violation_title.to_edge(UP, buff=1)
        violation_title.set_opacity(0)
        
        relative_holding = Text("亲属代持", font="SimHei", font_size=42, color=RED, weight=BOLD)
        relative_holding.move_to([0, 2, 0])
        relative_holding.set_opacity(0)
        
        abuse_power = Text("利用职务便利为家族商业谋利", font="SimHei", font_size=42, color=RED, weight=BOLD)
        abuse_power.move_to([0, -3, 0])
        abuse_power.set_opacity(0)
        
        official_confirm = Text("官方调查确认违规", font="SimHei", font_size=48, color=RED)
        official_confirm.move_to([0, -2, 0])
        official_confirm.set_opacity(0)
        
        # 确保元素不重叠并在屏幕边界内
        LayoutManager.ensure_screen_bounds(title)
        LayoutManager.ensure_screen_bounds(name)
        LayoutManager.ensure_screen_bounds(text_2010)
        LayoutManager.ensure_screen_bounds(text_2014)
        LayoutManager.ensure_screen_bounds(text_2017)
        LayoutManager.ensure_screen_bounds(violation_title)
        LayoutManager.ensure_screen_bounds(relative_holding)
        LayoutManager.ensure_screen_bounds(abuse_power)
        LayoutManager.ensure_screen_bounds(official_confirm)
        
        # 时间戳：0.0s ~ 2.0s
        # 音频/独白内容："杨伟于二零一零年考入雅安市城管局"
        character.move_to([3.5, 0, 0])
        self.play(
            FadeIn(title),
            FadeIn(name),
            character.animate.set_opacity(1),
            run_time=1
        )
        self.wait(1)
        
        # 时间戳：2.0s ~ 5.0s
        # 音频/独白内容："二零一零年考入雅安市城管局，二零一四年借调至投资促进局负责招商引资平台"
        # 人物照片缩小移至右上角
        small_character = character.copy()
        small_character.height = 2
        small_character.width = small_character.height * 0.75
        small_character.move_to([5, 2, 0])
        
        # 时间轴背景图从左侧滑入
        timeline.move_to([0, -2, 0])
        
        self.play(
            Transform(character, small_character),
            title.animate.set_opacity(0),
            name.animate.set_opacity(0),
            timeline.animate.set_opacity(1),
            run_time=1
        )
        
        # 2010年节点放大高亮显示
        self.play(
            FadeIn(text_2010),
            run_time=1
        )
        self.wait(1)
        
        # 时间戳：5.0s ~ 7.0s
        # 音频/独白内容："二零一四年借调至投资促进局负责招商引资平台"
        self.play(
            text_2010.animate.set_opacity(0.5),
            FadeIn(text_2014),
            run_time=1
        )
        self.wait(1)
        
        # 时间戳：7.0s ~ 8.0s
        # 音频/独白内容："二零一七年辞职。官方调查确认其在任公务员期间..."
        self.play(
            text_2014.animate.set_opacity(0.5),
            FadeIn(text_2017),
            run_time=0.5
        )
        self.wait(0.5)
        
        # 时间戳：8.0s ~ 10.0s
        # 音频/独白内容："...其在任公务员期间违规经商办企业，通过亲属代持方式关联多家公司"
        network.move_to([0, 0, 0])
        
        self.play(
            FadeOut(timeline),
            FadeOut(text_2010),
            FadeOut(text_2014),
            FadeOut(text_2017),
            FadeOut(character),
            FadeIn(network),
            FadeIn(violation_title),
            run_time=1
        )
        
        # "亲属代持"文字在约9秒处随音频内容出现
        self.wait(0.5)
        self.play(
            FadeIn(relative_holding),
            run_time=0.5
        )
        self.wait(0.5)
        
        # 时间戳：10.0s ~ 12.0s
        # 音频/独白内容："...涉嫌利用职务便利为家族商业活动谋取利益。"
        self.play(
            FadeIn(abuse_power),
            run_time=0.5
        )
        
        # 在11秒处，公章效果图带有0.5秒轻微"震撼"效果盖在画面上
        stamp.move_to([0, 0, 0])
        self.wait(0.5)
        self.play(
            FadeIn(stamp),
            stamp.animate.scale(1.05).scale(1/1.05),
            FadeIn(official_confirm),
            run_time=0.5
        )
        
        # 整个画面在12秒处淡出结束
        self.wait(0.5)
        self.play(
            *[FadeOut(mob) for mob in self.mobjects],
            run_time=0.5
        )