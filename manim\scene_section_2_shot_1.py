from manim import *
import os
from layout_manager import LayoutManager

class GraffEarringsControversy(Scene):
    def construct(self):
        # 设置背景色
        self.camera.background_color = "#FFFFFF"
        
        # 资产路径管理
        image_paths = {
            "ceremony_photo": "../data/assets/characters/yhdt.jpg",
            "character": "../data/assets/characters/character.jpg",
            "earrings": "../data/assets/props/yhdt_earring.jpg"
        }
        
        # 文件存在性检查
        for key, path in image_paths.items():
            if not os.path.exists(path):
                print(f"警告: 文件 {path} 不存在!")
        
        # 创建图片元素
        # 黄杨钿甜成人礼现场照 (宽高比 1.33)
        ceremony_photo = ImageMobject(image_paths["ceremony_photo"])
        ceremony_photo.height = 3.75
        ceremony_photo.width = 3.75 * 1.33  # 约5单位宽
        ceremony_photo.set_opacity(0)
        
        # 黄杨钿甜 (宽高比 0.75)
        character = ImageMobject(image_paths["character"])
        character.height = 4
        character.width = 4 * 0.75  # 约3单位宽
        character.set_opacity(0)
        
        # 耳环 (宽高比 1.00)
        earrings = ImageMobject(image_paths["earrings"])
        earrings.height = 3
        earrings.width = 3  # 正方形
        earrings.set_opacity(0)
        
        # 创建文字元素
        title1 = Text("天价耳环", font_size=48, color=BLACK, font="SimHei", weight=BOLD)
        title1.set_opacity(0)
        
        title2 = Text("成人礼照片曝光", font_size=40, color=BLACK, font="SimHei")
        title2.set_opacity(0)
        
        earrings_label = Text("GRAFF耳环", font_size=36, color=BLACK, font="SimHei")
        earrings_label.set_opacity(0)
        
        # 创建价格标签，数字部分为红色
        price_text = "市场估价约¥2,300,000"
        price_label = Text(price_text, font_size=40, font="SimHei")
        # 设置"¥2,300,000"部分为红色
        price_label[4:].set_color(RED)
        price_label.set_opacity(0)
        
        father_quote = Text("父亲称：这是仿制品", font_size=36, color=BLACK, font="SimHei")
        father_quote.set_opacity(0)
        
        no_proof = Text("未提供鉴定证明", font_size=32, color="#444444", font="SimHei", slant=ITALIC)
        no_proof.set_opacity(0)
        
        official_background = Text("公务员家庭背景", font_size=36, color="#000080", font="SimHei")
        official_background.set_opacity(0)
        
        luxury_item = Text("¥2,300,000奢侈品", font_size=36, color=RED, font="SimHei")
        luxury_item.set_opacity(0)
        
        conclusion = Text("财富来源引发质疑", font_size=44, color=BLACK, font="SimHei", weight=BOLD)
        conclusion.set_opacity(0)
        
        # 第一阶段 (0.0s-3.0s): 展示成人礼照片和标题
        # 将照片放在中心
        ceremony_photo.move_to(ORIGIN)
        title1.move_to([0, 3.5, 0])
        
        # 确保在屏幕边界内
        LayoutManager.ensure_screen_bounds(ceremony_photo)
        LayoutManager.ensure_screen_bounds(title1)
        
        # 调试信息
        LayoutManager.print_layout_debug(ceremony_photo, "成人礼照片")
        LayoutManager.print_layout_debug(title1, "天价耳环标题")
        
        # 照片从小到大展示，标题淡入
        self.play(
            FadeIn(ceremony_photo, scale=0.8),
            run_time=1
        )
        self.play(
            ceremony_photo.animate.scale(1.2),
            FadeIn(title1),
            run_time=2
        )
        
        # 第二阶段 (3.0s-6.0s): 展示人物和耳环
        # 设置人物和耳环位置
        character.move_to([-3.5, 0, 0])
        earrings.move_to([3.5, 0, 0])
        title2.move_to([0, 3.5, 0])
        
        # 确保在屏幕边界内
        LayoutManager.ensure_screen_bounds(character)
        LayoutManager.ensure_screen_bounds(earrings)
        LayoutManager.ensure_screen_bounds(title2)
        
        # 调试信息
        LayoutManager.print_layout_debug(character, "黄杨钿甜")
        LayoutManager.print_layout_debug(earrings, "GRAFF耳环")
        LayoutManager.print_layout_debug(title2, "成人礼照片曝光")
        
        # 成人礼照片缩小并移动到左侧，同时耳环从右侧滑入
        self.play(
            Transform(ceremony_photo, character),
            FadeOut(title1),
            FadeIn(title2),
            run_time=1.5
        )
        self.play(
            FadeIn(earrings, shift=RIGHT),
            run_time=1.5
        )
        
        # 第三阶段 (6.0s-9.0s): 展示耳环信息和价格
        # 设置耳环标签和价格标签位置
        earrings_label.move_to([3.5, -1.8, 0])
        price_label.move_to([3.5, -2.5, 0])
        
        # 确保在屏幕边界内
        LayoutManager.ensure_screen_bounds(earrings_label)
        LayoutManager.ensure_screen_bounds(price_label)
        
        # 调试信息
        LayoutManager.print_layout_debug(earrings_label, "GRAFF耳环标签")
        LayoutManager.print_layout_debug(price_label, "价格标签")
        
        # 耳环标签和价格标签出现
        self.play(
            FadeOut(title2),
            FadeIn(earrings_label),
            run_time=1
        )
        self.play(
            FadeIn(price_label, scale=1.2),
            run_time=2
        )
        
        # 第四阶段 (9.0s-11.0s): 展示父亲回应
        # 设置父亲回应和无证明文字位置
        father_quote.move_to([0, -2.0, 0])
        no_proof.move_to([0, -2.5, 0])
        
        # 确保在屏幕边界内
        LayoutManager.ensure_screen_bounds(father_quote)
        LayoutManager.ensure_screen_bounds(no_proof)
        
        # 调试信息
        LayoutManager.print_layout_debug(father_quote, "父亲回应")
        LayoutManager.print_layout_debug(no_proof, "无证明")
        
        # 父亲回应和无证明文字出现
        self.play(
            FadeIn(father_quote),
            run_time=1
        )
        self.play(
            FadeIn(no_proof),
            run_time=1
        )
        
        # 第五阶段 (11.0s-14.0s): 展示结论和对比
        # 缩小并上移人物和耳环图片
        character_small = character.copy().scale(0.83).shift(UP)
        earrings_small = earrings.copy().scale(0.83).shift(UP)
        
        # 设置背景对比和结论文字位置
        official_background.move_to([-3.5, -3.0, 0])
        luxury_item.move_to([3.5, -3.0, 0])
        conclusion.move_to([0, 3.5, 0])
        
        # 确保在屏幕边界内
        LayoutManager.ensure_screen_bounds(official_background)
        LayoutManager.ensure_screen_bounds(luxury_item)
        LayoutManager.ensure_screen_bounds(conclusion)
        
        # 调试信息
        LayoutManager.print_layout_debug(official_background, "公务员背景")
        LayoutManager.print_layout_debug(luxury_item, "奢侈品")
        LayoutManager.print_layout_debug(conclusion, "结论")
        
        # 图片缩小上移，对比文字和结论出现
        self.play(
            Transform(ceremony_photo, character_small),
            Transform(earrings, earrings_small),
            FadeOut(father_quote),
            FadeOut(no_proof),
            FadeOut(earrings_label),
            FadeOut(price_label),
            run_time=1
        )
        self.play(
            FadeIn(official_background),
            FadeIn(luxury_item),
            run_time=1
        )
        
        # 结论以打字机效果出现
        self.play(
            AddTextLetterByLetter(conclusion),
            run_time=2
        )
        
        # 最后停留
        self.wait(1)