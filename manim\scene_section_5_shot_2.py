from manim import *
import os
from layout_manager import LayoutManager

class SocialDiscussionAnimation(Scene):
    def construct(self):
        # 设置背景色为白色
        self.camera.background_color = "#FFFFFF"
        
        # 创建标题
        title = Text("社会讨论", font="SimHei", font_size=48, color="#00008B", weight=BOLD)
        title.to_edge(UP, buff=0.5)
        title.set_opacity(0)
        
        # 第一阶段 (0s-3s): 话题转变
        # 创建抽象图形素材_话题转变
        topic_transform_left = Text("娱乐/明星", font="SimHei", font_size=36, color="#00008B")
        topic_transform_right = Text("公共议题", font="SimHei", font_size=36, color="#00008B")
        arrow = Arrow(LEFT, RIGHT, color="#00008B", buff=0.5)
        
        topic_transform_group = VGroup(topic_transform_left, arrow, topic_transform_right)
        topic_transform_group.arrange(RIGHT, buff=0.8)
        topic_transform_group.move_to([0, -1, 0])
        topic_transform_group.set_opacity(0)
        
        # 标题淡入
        self.play(
            FadeIn(title),
            run_time=1
        )
        
        # 话题转变图形淡入
        self.play(
            FadeIn(topic_transform_group),
            run_time=2
        )
        
        # 第二阶段 (3s-7s): 公权力监督
        # 创建"娱乐话题→公共议题"文字
        topic_evolution = Text("娱乐话题→公共议题", font="SimHei", font_size=24, color="#00008B")
        topic_evolution.move_to([6, 0, 0])
        topic_evolution.set_opacity(0)
        
        # 创建公权力监督文字和图形
        public_power_text = Text("公权力监督", font="SimHei", font_size=36, color="#00008B", weight=BOLD)
        public_power_text.move_to([0, 0, 0])
        public_power_text.set_opacity(0)
        
        # 创建公权力监督图形素材
        magnifying_glass = SVGMobject("data/svg/magnifying_glass.svg", color="#00008B")
        eye = SVGMobject("data/svg/eye.svg", color="#00008B")
        document = SVGMobject("data/svg/document.svg", color="#00008B")
        
        # 检查文件是否存在
        for svg_file in ["data/svg/magnifying_glass.svg", "data/svg/eye.svg", "data/svg/document.svg"]:
            if not os.path.exists(svg_file):
                print(f"警告: 文件 {svg_file} 不存在，使用替代图形")
                
        # 如果文件不存在，使用替代图形
        supervision_elements = VGroup(
            Circle(radius=0.5, color="#00008B").set_fill("#00008B", opacity=0.3),
            Triangle(color="#00008B").set_fill("#00008B", opacity=0.3),
            Square(side_length=1, color="#00008B").set_fill("#00008B", opacity=0.3)
        )
        supervision_elements.arrange(RIGHT, buff=0.5)
        supervision_elements.scale(0.8)
        supervision_elements.next_to(public_power_text, DOWN, buff=0.5)
        supervision_elements.set_opacity(0)
        
        # 话题转变图形淡出，同时新元素淡入
        self.play(
            FadeOut(topic_transform_group),
            Write(topic_evolution),
            run_time=1
        )
        
        self.play(
            FadeIn(public_power_text),
            FadeIn(supervision_elements),
            run_time=2
        )
        
        # 缩小并移动公权力监督文字到右侧边缘
        public_power_text_small = Text("公权力监督", font="SimHei", font_size=24, color="#00008B")
        public_power_text_small.move_to([6, -0.5, 0])
        public_power_text_small.set_opacity(0)
        
        self.play(
            FadeIn(public_power_text_small),
            FadeOut(public_power_text),
            FadeOut(supervision_elements),
            run_time=1
        )
        
        # 第三阶段 (7s-12s): 仇腐不仇富
        # 创建仇腐不仇富对比图形
        wealth_side = VGroup(
            Circle(radius=0.8, color="#FFD700").set_fill("#FFD700", opacity=0.5),
            Text("合法财富", font="SimHei", font_size=24, color="#00008B")
        )
        wealth_side.arrange(DOWN, buff=0.3)
        
        corruption_side = VGroup(
            Circle(radius=0.8, color="#8B0000").set_fill("#8B0000", opacity=0.5),
            Text("腐败行为", font="SimHei", font_size=24, color="#00008B")
        )
        corruption_side.arrange(DOWN, buff=0.3)
        
        comparison_group = VGroup(wealth_side, Line(LEFT*2, RIGHT*2, color="#00008B"), corruption_side)
        comparison_group.arrange(RIGHT, buff=1.5)
        comparison_group.move_to([0, 0, 0])
        comparison_group.set_opacity(0)
        
        # 创建仇腐不仇富文字
        anti_corruption_text = Text("仇腐不仇富", font="SimHei", font_size=36, color="#00008B", weight=BOLD)
        anti_corruption_text.move_to([0, 2, 0])
        anti_corruption_text.set_opacity(0)
        
        # 显示仇腐不仇富内容
        self.play(
            FadeIn(comparison_group),
            FadeIn(anti_corruption_text),
            run_time=2
        )
        
        # 缩小并移动仇腐不仇富文字到右侧边缘
        anti_corruption_text_small = Text("仇腐不仇富", font="SimHei", font_size=24, color="#00008B")
        anti_corruption_text_small.move_to([6, -1, 0])
        anti_corruption_text_small.set_opacity(0)
        
        self.play(
            FadeIn(anti_corruption_text_small),
            run_time=1
        )
        
        self.play(
            FadeOut(anti_corruption_text),
            FadeOut(comparison_group),
            run_time=2
        )
        
        # 第四阶段 (12s-17s): 财富透明与廉洁
        # 创建财富来源透明性图形和文字
        wealth_transparency_text = Text("财富来源透明性", font="SimHei", font_size=32, color="#00008B", weight=BOLD)
        wealth_transparency_text.move_to([-3.5, 2, 0])
        wealth_transparency_text.set_opacity(0)
        
        # 创建财富来源透明度图形
        transparent_box = Square(side_length=2, color="#00008B")
        transparent_box.set_fill("#00008B", opacity=0.3)
        transparent_box.move_to([-3.5, 0, 0])
        transparent_box.set_opacity(0)
        
        # 创建公职人员廉洁性图形和文字
        integrity_text = Text("公职人员廉洁性", font="SimHei", font_size=32, color="#00008B", weight=BOLD)
        integrity_text.move_to([3.5, 2, 0])
        integrity_text.set_opacity(0)
        
        # 创建公职人员廉洁图形
        integrity_symbol = RegularPolygon(n=6, color="#00008B")
        integrity_symbol.set_fill("#00008B", opacity=0.3)
        integrity_symbol.move_to([3.5, 0, 0])
        integrity_symbol.set_opacity(0)
        
        # 连接线
        connection_line = Line([-1.5, 0, 0], [1.5, 0, 0], color="#00008B")
        connection_line.set_opacity(0)
        
        # 显示财富透明与廉洁内容
        self.play(
            FadeIn(wealth_transparency_text),
            FadeIn(transparent_box),
            FadeIn(integrity_text),
            FadeIn(integrity_symbol),
            FadeIn(connection_line),
            run_time=2
        )
        
        # 创建财产透明与廉洁文字（右侧边缘）
        transparency_integrity_text = Text("财产透明与廉洁", font="SimHei", font_size=24, color="#00008B")
        transparency_integrity_text.move_to([6, -1.5, 0])
        transparency_integrity_text.set_opacity(0)
        
        self.play(
            FadeIn(transparency_integrity_text),
            run_time=1
        )
        
        self.wait(2)
        
        # 淡出当前内容
        self.play(
            FadeOut(wealth_transparency_text),
            FadeOut(transparent_box),
            FadeOut(integrity_text),
            FadeOut(integrity_symbol),
            FadeOut(connection_line),
            run_time=1
        )
        
        # 第五阶段 (17s-21s): 特权阶层担忧与社会监督
        # 创建特权阶层图形素材
        pyramid = VGroup()
        layers = 5
        for i in range(layers):
            width = 4 - i * 0.8
            layer = Rectangle(width=width, height=0.6, color="#00008B")
            layer.set_fill("#00008B", opacity=(i+1)/(layers+1))
            layer.move_to([0, -2 + i * 0.6, 0])
            pyramid.add(layer)
        
        # 添加特权阶层标签
        privilege_label = Text("特权阶层", font="SimHei", font_size=24, color="#00008B")
        privilege_label.next_to(pyramid.submobjects[-1], UP, buff=0.2)
        
        pyramid_group = VGroup(pyramid, privilege_label)
        pyramid_group.move_to([0, 0, 0])
        pyramid_group.set_opacity(0)
        
        # 创建特权阶层担忧文字（右侧边缘）
        privilege_concern_text = Text("特权阶层担忧", font="SimHei", font_size=24, color="#00008B")
        privilege_concern_text.move_to([6, -2, 0])
        privilege_concern_text.set_opacity(0)
        
        # 创建呼吁社会监督文字
        social_supervision_text = Text("呼吁社会监督", font="SimHei", font_size=36, color="#00008B", weight=BOLD)
        social_supervision_text.move_to([0, -3, 0])
        social_supervision_text.set_opacity(0)
        
        # 显示特权阶层内容
        self.play(
            FadeIn(pyramid_group),
            FadeIn(privilege_concern_text),
            run_time=2
        )
        
        # 显示最终结论
        self.play(
            FadeIn(social_supervision_text),
            run_time=1
        )
        
        # 等待最后一秒
        self.wait(1)
        
        # 淡出所有内容
        all_objects = VGroup(
            title, topic_evolution, public_power_text_small, 
            anti_corruption_text_small, transparency_integrity_text,
            privilege_concern_text, social_supervision_text, pyramid_group
        )
        
        self.play(
            FadeOut(all_objects),
            run_time=1
        )

# 确保LayoutManager类存在，如果不存在则创建一个简单的实现
if 'LayoutManager' not in globals():
    class LayoutManager:
        @staticmethod
        def get_safe_position_right_of(left_object, right_object, margin=0.5):
            return left_object.get_right() + RIGHT * margin + RIGHT * right_object.width/2
        
        @staticmethod
        def get_safe_position_left_of(right_object, left_object, margin=0.5):
            return right_object.get_left() + LEFT * margin + LEFT * left_object.width/2
        
        @staticmethod
        def get_safe_position_above(bottom_object, top_object, margin=0.3):
            return bottom_object.get_top() + UP * margin + UP * top_object.height/2
        
        @staticmethod
        def get_safe_position_below(top_object, bottom_object, margin=0.3):
            return top_object.get_bottom() + DOWN * margin + DOWN * bottom_object.height/2
        
        @staticmethod
        def check_overlap(obj1, obj2, margin=0.1):
            return obj1.get_center()[0] - obj1.width/2 - margin < obj2.get_center()[0] + obj2.width/2 + margin and \
                   obj1.get_center()[0] + obj1.width/2 + margin > obj2.get_center()[0] - obj2.width/2 - margin and \
                   obj1.get_center()[1] - obj1.height/2 - margin < obj2.get_center()[1] + obj2.height/2 + margin and \
                   obj1.get_center()[1] + obj1.height/2 + margin > obj2.get_center()[1] - obj2.height/2 - margin
        
        @staticmethod
        def print_layout_debug(obj, name):
            print(f"元素 {name} 位置: {obj.get_center()}, 宽度: {obj.width}, 高度: {obj.height}")
        
        @staticmethod
        def ensure_screen_bounds(obj, screen_width=14, screen_height=8, margin=0.2):
            center = obj.get_center()
            width, height = obj.width, obj.height
            
            # 检查并调整X坐标
            if center[0] - width/2 < -screen_width/2 + margin:
                center[0] = -screen_width/2 + width/2 + margin
            elif center[0] + width/2 > screen_width/2 - margin:
                center[0] = screen_width/2 - width/2 - margin
                
            # 检查并调整Y坐标
            if center[1] - height/2 < -screen_height/2 + margin:
                center[1] = -screen_height/2 + height/2 + margin
            elif center[1] + height/2 > screen_height/2 - margin:
                center[1] = screen_height/2 - height/2 - margin
                
            obj.move_to(center)
            return obj