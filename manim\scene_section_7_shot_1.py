from manim import *
import os
from layout_manager import LayoutManager

class HuangYangDianTianEarringEvent(Scene):
    def construct(self):
        # 设置背景色
        self.camera.background_color = WHITE
        
        # ===== 0.0s ~ 3.0s =====
        # 创建标题文字
        title = Text("要点回顾", font="SimHei", font_size=28, color="#1A365D", weight=BOLD)
        title.set_opacity(0)
        
        subtitle = Text("黄杨钿甜耳环事件", font="SimHei", font_size=22, color="#1A365D")
        subtitle.next_to(title, DOWN, buff=0.5)
        subtitle.set_opacity(0)
        
        # 创建分隔线
        separator = Line(LEFT * 5, RIGHT * 5, color="#1A365D")
        separator.next_to(subtitle, DOWN, buff=0.5)
        separator.set_opacity(0)
        
        # 标题动画
        self.play(
            FadeIn(title, scale=1.2),
            run_time=1
        )
        
        # 打字机效果显示副标题
        self.play(AddTextLetterByLetter(subtitle), run_time=1.5)
        
        # 显示分隔线
        self.play(Create(separator), run_time=0.5)
        
        # ===== 4.0s ~ 10.0s =====
        # 创建事件演变部分的文字
        event_evolution_title = Text("事件演变", font="SimHei", font_size=20, color="#1A365D", weight=BOLD)
        event_evolution_title.move_to([-5, 3, 0])
        event_evolution_title.set_opacity(0)
        
        single_star_controversy = Text("单一明星争议", font="SimHei", font_size=18, color=BLACK)
        single_star_controversy.move_to([-5, 2, 0])
        single_star_controversy.set_opacity(0)
        
        public_event = Text("公共事件", font="SimHei", font_size=18, color=BLACK)
        public_event.move_to([-5, 0, 0])
        public_event.set_opacity(0)
        
        # 创建箭头
        arrow = Arrow([-5, 1.5, 0], [-5, 0.5, 0], buff=0.1, color=GRAY)
        arrow.set_opacity(0)
        
        # 创建核心问题文字
        core_issue = Text("公职人员家庭财富与职位不相符", font="SimHei", font_size=20, color="#1A365D", weight=BOLD)
        core_issue.move_to([0, -3, 0])
        core_issue.set_opacity(0)
        
        # 淡入事件演变标题
        self.play(FadeIn(event_evolution_title), run_time=0.5)
        
        # 淡入单一明星争议文字
        self.play(FadeIn(single_star_controversy), run_time=0.5)
        
        # 显示箭头动画
        self.play(GrowArrow(arrow), run_time=1)
        
        # 淡入公共事件文字
        self.play(FadeIn(public_event), run_time=0.5)
        
        # 高亮显示核心问题
        self.play(
            FadeIn(core_issue, scale=1.2),
            Flash(core_issue, color="#1A365D", flash_radius=0.3),
            run_time=1.5
        )
        
        # 等待一段时间
        self.wait(2)
        
        # 淡出所有元素
        self.play(
            *[FadeOut(mob) for mob in [
                title, subtitle, separator, 
                event_evolution_title, single_star_controversy, 
                arrow, public_event, core_issue
            ]],
            run_time=1
        )
        
        # ===== 11.0s ~ 14.0s =====
        # 创建已确认问题部分
        confirmed_issues_title = Text("已确认问题", font="SimHei", font_size=20, color="#2D6A4F", weight=BOLD)
        confirmed_issues_title.move_to([0, 3, 0])
        confirmed_issues_title.set_opacity(0)
        
        # 创建绿色对勾图标
        checkmark1 = Text("✓", font_size=24, color="#2D6A4F")
        checkmark1.move_to([-2.5, 1, 0])
        checkmark1.set_opacity(0)
        
        checkmark2 = Text("✓", font_size=24, color="#2D6A4F")
        checkmark2.move_to([1.5, 1, 0])
        checkmark2.set_opacity(0)
        
        # 创建已确认问题文字
        illegal_business = Text("违规经商", font="SimHei", font_size=18, color=BLACK)
        illegal_business.move_to([-2, 1, 0])
        illegal_business.set_opacity(0)
        
        hidden_birth = Text("隐瞒违法生育", font="SimHei", font_size=18, color=BLACK)
        hidden_birth.move_to([2, 1, 0])
        hidden_birth.set_opacity(0)
        
        # 确保布局安全
        LayoutManager.ensure_screen_bounds(confirmed_issues_title)
        LayoutManager.ensure_screen_bounds(illegal_business)
        LayoutManager.ensure_screen_bounds(hidden_birth)
        
        # 淡入已确认问题标题
        self.play(FadeIn(confirmed_issues_title), run_time=0.5)
        
        # 显示第一个问题点
        self.play(
            FadeIn(checkmark1, scale=1.2),
            FadeIn(illegal_business),
            run_time=0.75
        )
        
        # 显示第二个问题点
        self.play(
            FadeIn(checkmark2, scale=1.2),
            FadeIn(hidden_birth),
            run_time=0.75
        )
        
        # 等待一段时间
        self.wait(1)
        
        # 淡出所有元素
        self.play(
            *[FadeOut(mob) for mob in [
                confirmed_issues_title, 
                checkmark1, illegal_business,
                checkmark2, hidden_birth
            ]],
            run_time=1
        )
        
        # ===== 15.0s ~ 18.0s =====
        # 创建未解决问题部分
        unsolved_issues_title = Text("未解决问题", font="SimHei", font_size=20, color="#9D0208", weight=BOLD)
        unsolved_issues_title.move_to([0, 3, 0])
        unsolved_issues_title.set_opacity(0)
        
        # 创建红色问号图标
        question_mark1 = Text("?", font_size=24, color="#9D0208")
        question_mark1.move_to([-2, 2, 0])
        question_mark1.set_opacity(0)
        
        question_mark2 = Text("?", font_size=24, color="#9D0208")
        question_mark2.move_to([-2, 0, 0])
        question_mark2.set_opacity(0)
        
        question_mark3 = Text("?", font_size=24, color="#9D0208")
        question_mark3.move_to([-2, -2, 0])
        question_mark3.set_opacity(0)
        
        # 创建未解决问题文字
        earring_authenticity = Text("耳环真伪", font="SimHei", font_size=18, color=BLACK)
        earring_authenticity.move_to([0, 2, 0])
        earring_authenticity.set_opacity(0)
        
        family_wealth = Text("家族巨额财富来源", font="SimHei", font_size=18, color=BLACK)
        family_wealth.move_to([0, 0, 0])
        family_wealth.set_opacity(0)
        
        power_business = Text("权力与商业的利益输送", font="SimHei", font_size=18, color=BLACK)
        power_business.move_to([0, -2, 0])
        power_business.set_opacity(0)
        
        # 确保布局安全
        LayoutManager.ensure_screen_bounds(unsolved_issues_title)
        LayoutManager.ensure_screen_bounds(earring_authenticity)
        LayoutManager.ensure_screen_bounds(family_wealth)
        LayoutManager.ensure_screen_bounds(power_business)
        
        # 淡入未解决问题标题
        self.play(FadeIn(unsolved_issues_title), run_time=0.5)
        
        # 显示第一个未解决问题
        self.play(
            FadeIn(question_mark1),
            FadeIn(earring_authenticity),
            Flash(question_mark1, color="#9D0208", flash_radius=0.2),
            run_time=0.75
        )
        
        # 显示第二个未解决问题
        self.play(
            FadeIn(question_mark2),
            FadeIn(family_wealth),
            Flash(question_mark2, color="#9D0208", flash_radius=0.2),
            run_time=0.75
        )
        
        # 显示第三个未解决问题
        self.play(
            FadeIn(question_mark3),
            FadeIn(power_business),
            Flash(question_mark3, color="#9D0208", flash_radius=0.2),
            run_time=0.75
        )
        
        # 等待一段时间
        self.wait(0.25)
        
        # 淡出所有元素
        self.play(
            *[FadeOut(mob) for mob in [
                unsolved_issues_title,
                question_mark1, earring_authenticity,
                question_mark2, family_wealth,
                question_mark3, power_business
            ]],
            run_time=1
        )
        
        # ===== 19.0s ~ 24.0s =====
        # 创建右侧渐变背景
        gradient_rect = Rectangle(
            width=7, height=8,
            fill_opacity=0.2,
            fill_color=["#E0AAFF", "#5A189A"],
            stroke_width=0
        )
        gradient_rect.move_to([3.5, 0, 0])
        gradient_rect.set_opacity(0)
        
        # 创建社会启示部分
        social_implications_title = Text("社会启示", font="SimHei", font_size=20, color="#5A189A", weight=BOLD)
        social_implications_title.move_to([5, 3, 0])
        social_implications_title.set_opacity(0)
        
        trust_crisis = Text("信任危机", font="SimHei", font_size=22, color="#5A189A", weight=BOLD)
        trust_crisis.move_to([5, 0, 0])
        trust_crisis.set_opacity(0)
        
        property_supervision = Text("财产监督制度", font="SimHei", font_size=22, color="#5A189A", weight=BOLD)
        property_supervision.move_to([5, -2, 0])
        property_supervision.set_opacity(0)
        
        conclusion = Text("完善监督，刻不容缓", font="SimHei", font_size=18, color="#5A189A", slant=ITALIC)
        conclusion.move_to([5, -3.5, 0])
        conclusion.set_opacity(0)
        
        # 确保布局安全
        LayoutManager.ensure_screen_bounds(social_implications_title)
        LayoutManager.ensure_screen_bounds(trust_crisis)
        LayoutManager.ensure_screen_bounds(property_supervision)
        LayoutManager.ensure_screen_bounds(conclusion)
        
        # 显示渐变背景
        self.play(FadeIn(gradient_rect), run_time=0.5)
        
        # 淡入社会启示标题
        self.play(FadeIn(social_implications_title), run_time=0.5)
        
        # 显示信任危机关键词
        self.play(
            FadeIn(trust_crisis, scale=1.2),
            Flash(trust_crisis, color="#5A189A", flash_radius=0.3),
            run_time=1
        )
        
        # 显示财产监督制度关键词
        self.play(
            FadeIn(property_supervision, scale=1.2),
            Flash(property_supervision, color="#5A189A", flash_radius=0.3),
            run_time=1
        )
        
        # 淡入结语
        self.play(FadeIn(conclusion), run_time=1)
        
        # 等待一段时间
        self.wait(1)
        
        # 最终淡出
        self.play(
            *[FadeOut(mob) for mob in [
                gradient_rect,
                social_implications_title,
                trust_crisis,
                property_supervision,
                conclusion
            ]],
            run_time=1
        )