from manim import *
import os
from layout_manager import LayoutManager

class OfficialInvestigationAnimation(Scene):
    def construct(self):
        # 设置背景色为白色
        self.camera.background_color = "#FFFFFF"
        
        # 创建水印文本（贯穿整个动画）
        watermark = Text("官方调查确认", font="SimHei", font_size=32, color=BLACK)
        watermark.move_to([0, -3.5, 0])
        watermark.set_opacity(0.7)
        
        # 第一阶段 (0s-3s): 官方调查确认杨伟隐瞒违法生育二孩问题
        # 创建标题和副标题
        title = Text("官方调查确认", font="SimHei", font_size=48, color=BLACK, weight=BOLD)
        title.move_to([0, 2.5, 0])
        title.set_opacity(0)
        
        # 创建带有高亮的副标题
        subtitle_text = "杨伟隐瞒违法生育二孩问题"
        subtitle = Text(subtitle_text, font="SimHei", font_size=42, color=BLACK)
        subtitle.move_to([0, 0, 0])
        subtitle.set_opacity(0)
        
        # 高亮"违法生育"部分
        highlight_start = subtitle_text.find("违法生育")
        highlight_end = highlight_start + len("违法生育")
        subtitle[highlight_start:highlight_end].set_color(RED)
        
        # 创建序号
        number1 = Text("1", font="SimHei", font_size=36, color=BLACK, weight=BOLD)
        number1.move_to([-6, 3, 0])
        number1.set_opacity(0)
        
        # 检查资产路径
        header_path = "Family_Investigation_Header"
        if not os.path.exists(header_path):
            print(f"警告: 文件 {header_path} 不存在")
        
        # 添加元素到场景并播放动画
        self.add(watermark)
        
        # 标题从上方滑入
        self.play(
            title.animate.set_opacity(1).shift(DOWN * 0.5),
            run_time=0.8,
            rate_func=smooth
        )
        
        # 序号淡入
        self.play(
            FadeIn(number1),
            run_time=0.5
        )
        
        # 副标题打字机效果
        self.play(
            AddTextLetterByLetter(subtitle),
            run_time=1.7
        )
        
        # 等待一会
        self.wait(0.5)
        
        # 第二阶段 (4s-8s): 二孩政策时间线
        # 创建新标题和内容
        title2 = Text("二孩政策时间线", font="SimHei", font_size=42, color=BLACK)
        title2.move_to([0, 3, 0])
        title2.set_opacity(0)
        
        # 创建时间点文本
        time_text1 = "2011年 - 黄杨钿甜弟弟出生"
        time_point1 = Text(time_text1, font="SimHei", font_size=36, color=BLACK)
        time_point1.move_to([-3, 0, 0])
        time_point1.set_opacity(0)
        
        # 高亮2011年
        highlight_start = time_text1.find("2011年")
        highlight_end = highlight_start + len("2011年")
        time_point1[highlight_start:highlight_end].set_color(RED)
        
        time_text2 = "2016年 - 全面二孩政策实施"
        time_point2 = Text(time_text2, font="SimHei", font_size=36, color=BLACK)
        time_point2.move_to([3, 0, 0])
        time_point2.set_opacity(0)
        
        # 高亮2016年
        highlight_start = time_text2.find("2016年")
        highlight_end = highlight_start + len("2016年")
        time_point2[highlight_start:highlight_end].set_color(RED)
        
        # 创建序号
        number2 = Text("2", font="SimHei", font_size=36, color=BLACK, weight=BOLD)
        number2.move_to([-6, 3, 0])
        number2.set_opacity(0)
        
        # 检查资产路径
        timeline_path = "Timeline_Comparison"
        if not os.path.exists(timeline_path):
            print(f"警告: 文件 {timeline_path} 不存在")
        
        # 创建时间轴连接线
        timeline = Line(time_point1.get_right() + LEFT * 0.5, time_point2.get_left() + RIGHT * 0.5, color=GRAY)
        timeline.set_opacity(0)
        
        # 淡出第一阶段元素
        self.play(
            FadeOut(title),
            FadeOut(subtitle),
            FadeOut(number1),
            run_time=0.5
        )
        
        # 淡入第二阶段元素
        self.play(
            FadeIn(title2),
            FadeIn(number2),
            run_time=0.8
        )
        
        # 显示2011年时间点
        self.play(
            FadeIn(time_point1),
            run_time=0.8
        )
        
        # 放大强调2011年
        self.play(
            time_point1[highlight_start:highlight_end].animate.scale(1.2),
            run_time=0.5,
            rate_func=there_and_back
        )
        
        # 显示时间轴
        self.play(
            FadeIn(timeline),
            run_time=0.5
        )
        
        # 显示2016年时间点
        self.play(
            FadeIn(time_point2),
            run_time=0.8
        )
        
        # 放大强调2016年
        self.play(
            time_point2[highlight_start:highlight_end].animate.scale(1.2),
            run_time=0.5,
            rate_func=there_and_back
        )
        
        # 等待一会
        self.wait(0.4)
        
        # 第三阶段 (9s-14s): 家族姓氏关系
        # 创建新标题和内容
        title3 = Text("家族姓氏关系", font="SimHei", font_size=42, color=BLACK)
        title3.move_to([0, 3, 0])
        title3.set_opacity(0)
        
        # 创建家族成员文本和框
        grandmother = Text("奶奶(黄)", font="SimHei", font_size=36, color=BLACK)
        grandmother.move_to([0, 2, 0])
        grandmother_box = SurroundingRectangle(grandmother, color=YELLOW, buff=0.2, corner_radius=0.2)
        grandmother_group = VGroup(grandmother, grandmother_box)
        grandmother_group.set_opacity(0)
        
        father = Text("父亲(杨)", font="SimHei", font_size=36, color=BLACK)
        father.move_to([-2, 0, 0])
        father_box = SurroundingRectangle(father, color=BLUE, buff=0.2, corner_radius=0.2)
        father_group = VGroup(father, father_box)
        father_group.set_opacity(0)
        
        mother = Text("母亲(钿)", font="SimHei", font_size=36, color=BLACK)
        mother.move_to([2, 0, 0])
        mother_box = SurroundingRectangle(mother, color=PURPLE, buff=0.2, corner_radius=0.2)
        mother_group = VGroup(mother, mother_box)
        mother_group.set_opacity(0)
        
        daughter = Text("黄杨钿甜", font="SimHei", font_size=36, color=BLACK)
        daughter.move_to([-2, -2, 0])
        daughter_box = SurroundingRectangle(daughter, color=YELLOW, buff=0.2, corner_radius=0.2)
        daughter_group = VGroup(daughter, daughter_box)
        daughter_group.set_opacity(0)
        
        son = Text("弟弟(未公开姓名)", font="SimHei", font_size=36, color=BLACK)
        son.move_to([2, -2, 0])
        son_box = SurroundingRectangle(son, color=GRAY, buff=0.2, corner_radius=0.2)
        son_group = VGroup(son, son_box)
        son_group.set_opacity(0)
        
        # 创建连接线
        line_gm_father = Line(grandmother.get_bottom() + DOWN * 0.2, father.get_top() + UP * 0.2, color=BLUE)
        line_gm_mother = Line(grandmother.get_bottom() + DOWN * 0.2, mother.get_top() + UP * 0.2, color=PURPLE)
        line_father_daughter = Line(father.get_bottom() + DOWN * 0.2, daughter.get_top() + UP * 0.2, color=BLUE)
        line_father_son = Line(father.get_bottom() + DOWN * 0.2, son.get_top() + UP * 0.2, color=BLUE)
        line_mother_daughter = Line(mother.get_bottom() + DOWN * 0.2, daughter.get_top() + UP * 0.2, color=PURPLE)
        line_mother_son = Line(mother.get_bottom() + DOWN * 0.2, son.get_top() + UP * 0.2, color=PURPLE)
        
        # 特殊连接线 - 奶奶到黄杨钿甜（强调随奶奶姓）
        line_gm_daughter = Arrow(grandmother.get_bottom() + DOWN * 0.1, daughter.get_top() + UP * 0.1, 
                                color=YELLOW, buff=0.3, stroke_width=3)
        
        lines_group = VGroup(line_gm_father, line_gm_mother, line_father_daughter, 
                            line_father_son, line_mother_daughter, line_mother_son, line_gm_daughter)
        lines_group.set_opacity(0)
        
        # 创建序号
        number3 = Text("3", font="SimHei", font_size=36, color=BLACK, weight=BOLD)
        number3.move_to([-6, 3, 0])
        number3.set_opacity(0)
        
        # 检查资产路径
        family_chart_path = "Family_Structure_Chart"
        if not os.path.exists(family_chart_path):
            print(f"警告: 文件 {family_chart_path} 不存在")
        
        # 淡出第二阶段元素
        self.play(
            FadeOut(title2),
            FadeOut(time_point1),
            FadeOut(time_point2),
            FadeOut(timeline),
            FadeOut(number2),
            run_time=0.5
        )
        
        # 淡入第三阶段元素
        self.play(
            FadeIn(title3),
            FadeIn(number3),
            run_time=0.8
        )
        
        # 显示奶奶
        self.play(
            FadeIn(grandmother_group),
            run_time=0.8
        )
        
        # 显示父母
        self.play(
            FadeIn(father_group),
            FadeIn(mother_group),
            FadeIn(line_gm_father),
            FadeIn(line_gm_mother),
            run_time=0.8
        )
        
        # 显示子女
        self.play(
            FadeIn(daughter_group),
            FadeIn(son_group),
            FadeIn(line_father_daughter),
            FadeIn(line_father_son),
            FadeIn(line_mother_daughter),
            FadeIn(line_mother_son),
            run_time=0.8
        )
        
        # 特别强调奶奶与黄杨钿甜的姓氏关系
        self.play(
            FadeIn(line_gm_daughter),
            run_time=0.8
        )
        
        # 强调黄杨钿甜随奶奶姓的特殊性
        self.play(
            daughter_box.animate.set_color(YELLOW).set_stroke_width(4),
            grandmother_box.animate.set_color(YELLOW).set_stroke_width(4),
            line_gm_daughter.animate.set_color(YELLOW).set_stroke_width(4),
            run_time=1,
            rate_func=there_and_back_with_pause
        )
        
        # 等待一会
        self.wait(0.4)
        
        # 第四阶段 (15s-18s): 官方通报结论
        # 创建新标题和内容
        title4 = Text("官方通报结论", font="SimHei", font_size=42, color=BLACK, weight=BOLD)
        title4.move_to([0, 3, 0])
        title4.set_opacity(0)
        
        conclusion_text = "已确认杨伟违法生育二孩问题"
        conclusion = Text(conclusion_text, font="SimHei", font_size=36, color=BLACK)
        conclusion.move_to([0, 0, 0])
        conclusion.set_opacity(0)
        
        # 高亮"杨伟违法生育二孩"部分
        highlight_start = conclusion_text.find("杨伟违法生育二孩")
        highlight_end = highlight_start + len("杨伟违法生育二孩")
        conclusion[highlight_start:highlight_end].set_color(RED)
        
        # 创建序号
        number4 = Text("4", font="SimHei", font_size=36, color=BLACK, weight=BOLD)
        number4.move_to([-6, 3, 0])
        number4.set_opacity(0)
        
        # 创建官方印章效果
        stamp = Circle(radius=2, color=RED)
        stamp.set_fill(RED, opacity=0.1)
        stamp.set_stroke(RED, opacity=0.8, width=2)
        stamp.move_to([0, 0, 0])
        stamp.scale(0)
        
        # 检查资产路径
        conclusion_path = "Official_Conclusion"
        if not os.path.exists(conclusion_path):
            print(f"警告: 文件 {conclusion_path} 不存在")
        
        # 淡出第三阶段元素
        self.play(
            FadeOut(title3),
            FadeOut(grandmother_group),
            FadeOut(father_group),
            FadeOut(mother_group),
            FadeOut(daughter_group),
            FadeOut(son_group),
            FadeOut(lines_group),
            FadeOut(number3),
            run_time=0.5
        )
        
        # 淡入第四阶段元素
        self.play(
            FadeIn(title4),
            FadeIn(number4),
            run_time=0.8
        )
        
        # 印章效果展示结论
        self.play(
            stamp.animate.scale(1),
            FadeIn(conclusion),
            run_time=1,
            rate_func=smooth
        )
        
        # 强调关键词
        self.play(
            conclusion[highlight_start:highlight_end].animate.scale(1.2),
            run_time=0.8,
            rate_func=there_and_back_with_pause
        )
        
        # 最后淡出水印
        self.play(
            FadeOut(watermark),
            run_time=1
        )
        
        # 等待一会
        self.wait(0.2)