from manim import *
import os
from layout_manager import LayoutManager

class PublicOfficialSupervisionAnimation(Scene):
    def construct(self):
        # 设置背景色为白色
        self.camera.background_color = "#FFFFFF"
        
        # 资产路径检查函数
        def check_asset(path):
            if not os.path.exists(path):
                print(f"警告: 资产文件不存在 - {path}")
                return False
            return True
        
        # 资产路径定义
        briefcase_path = "公文包图标.png"
        seal_path = "印章图标.png"
        empty_form_path = "空白表格图形.png"
        broken_seal_path = "破损图章图形.png"
        relationship_network_path = "模糊关系网图形.png"
        supervision_mechanism_path = "完善监督机制图形.png"
        anti_corruption_path = "防止腐败图形.png"
        
        # 检查资产文件是否存在
        check_asset(briefcase_path)
        check_asset(seal_path)
        check_asset(empty_form_path)
        check_asset(broken_seal_path)
        check_asset(relationship_network_path)
        check_asset(supervision_mechanism_path)
        check_asset(anti_corruption_path)
        
        # 第一阶段 (0s-2s): 监管困境介绍
        # 创建公文包图标
        briefcase_icon = ImageMobject(briefcase_path)
        briefcase_icon.height = 2
        briefcase_icon.width = 2  # 宽高比1:1
        briefcase_icon.move_to([-4, 0, 0])
        briefcase_icon.set_opacity(0)
        
        # 创建印章图标
        seal_icon = ImageMobject(seal_path)
        seal_icon.height = 2
        seal_icon.width = 2  # 宽高比1:1
        seal_icon.move_to([4, 0, 0])
        seal_icon.set_opacity(0)
        
        # 创建标题文字
        title_text = Text("监管困境", font="SimHei", font_size=48, color="#00008B")
        title_text.move_to([0, 0, 0])
        title_text.set_opacity(0)
        
        # 创建副标题文字
        subtitle_text = Text("公职人员监督管理漏洞", font="SimHei", font_size=36, color="#444444")
        subtitle_text.move_to([0, -1, 0])
        subtitle_text.set_opacity(0)
        
        # 确保元素不重叠
        LayoutManager.ensure_screen_bounds(briefcase_icon)
        LayoutManager.ensure_screen_bounds(seal_icon)
        LayoutManager.ensure_screen_bounds(title_text)
        LayoutManager.ensure_screen_bounds(subtitle_text)
        
        # 第一阶段动画
        self.play(
            FadeIn(briefcase_icon, rate_func=smooth),
            Rotate(briefcase_icon, angle=0.1, rate_func=there_and_back_with_pause),
            FadeIn(seal_icon, rate_func=smooth),
            Rotate(seal_icon, angle=-0.1, rate_func=there_and_back_with_pause),
            run_time=0.8
        )
        
        self.play(
            FadeIn(title_text, rate_func=smooth),
            Scale(title_text, 1.1, rate_func=there_and_back_with_pause),
            run_time=0.7
        )
        
        self.play(
            FadeIn(subtitle_text, rate_func=smooth),
            run_time=0.5
        )
        
        # 第二阶段 (2s-5s): 财产申报制度执行不力
        # 创建空白表格图形
        empty_form = ImageMobject(empty_form_path)
        empty_form.height = 3
        empty_form.width = 4  # 宽高比1.33:1
        empty_form.move_to([2, 0, 0])
        empty_form.set_opacity(0)
        
        # 创建关键词文字
        issue1_text = Text("财产申报制度执行不力", font="SimHei", font_size=36, color="#FF8C00")
        issue1_text.move_to([-3, 0, 0])
        issue1_text.set_opacity(0)
        
        # 创建说明文字
        desc1_text = Text("表格缺失、执行形式化", font="SimHei", font_size=28, color="#444444")
        desc1_text.move_to([-3, -1, 0])
        desc1_text.set_opacity(0)
        
        # 确保元素不重叠
        LayoutManager.ensure_screen_bounds(empty_form)
        LayoutManager.ensure_screen_bounds(issue1_text)
        LayoutManager.ensure_screen_bounds(desc1_text)
        
        # 第二阶段动画
        self.play(
            FadeOut(briefcase_icon),
            FadeOut(seal_icon),
            FadeOut(title_text),
            FadeOut(subtitle_text),
            run_time=0.5
        )
        
        self.play(
            FadeIn(empty_form, rate_func=smooth),
            empty_form.animate.shift(RIGHT * 0.1).shift(LEFT * 0.1),  # 轻微震动效果
            run_time=0.8
        )
        
        self.play(
            FadeIn(issue1_text, rate_func=smooth),
            run_time=0.7
        )
        
        self.play(
            FadeIn(desc1_text, rate_func=smooth),
            run_time=0.5
        )
        
        self.wait(0.5)
        
        # 第三阶段 (5s-8s): 离职审计机制不健全
        # 创建破损图章图形
        broken_seal = ImageMobject(broken_seal_path)
        broken_seal.height = 3
        broken_seal.width = 4  # 宽高比1.33:1
        broken_seal.move_to([2, 0, 0])
        broken_seal.set_opacity(0)
        
        # 创建关键词文字
        issue2_text = Text("离职审计机制不健全", font="SimHei", font_size=36, color="#FF8C00")
        issue2_text.move_to([-3, 0, 0])
        issue2_text.set_opacity(0)
        
        # 创建说明文字
        desc2_text = Text("流程不完整、监督缺失", font="SimHei", font_size=28, color="#444444")
        desc2_text.move_to([-3, -1, 0])
        desc2_text.set_opacity(0)
        
        # 确保元素不重叠
        LayoutManager.ensure_screen_bounds(broken_seal)
        LayoutManager.ensure_screen_bounds(issue2_text)
        LayoutManager.ensure_screen_bounds(desc2_text)
        
        # 第三阶段动画
        self.play(
            FadeOut(empty_form),
            FadeOut(issue1_text),
            FadeOut(desc1_text),
            run_time=0.5
        )
        
        self.play(
            FadeIn(broken_seal, rate_func=smooth),
            run_time=0.8
        )
        
        # 破损部分闪烁效果
        self.play(
            broken_seal.animate.set_opacity(0.8).set_opacity(1),
            run_time=0.5
        )
        
        self.play(
            FadeIn(issue2_text, rate_func=smooth),
            run_time=0.7
        )
        
        self.play(
            FadeIn(desc2_text, rate_func=smooth),
            run_time=0.5
        )
        
        self.wait(0.5)
        
        # 第四阶段 (8s-11s): 亲属经商监管不严
        # 创建模糊关系网图形
        relationship_network = ImageMobject(relationship_network_path)
        relationship_network.height = 3
        relationship_network.width = 4  # 宽高比1.33:1
        relationship_network.move_to([2, 0, 0])
        relationship_network.set_opacity(0)
        
        # 创建关键词文字
        issue3_text = Text("亲属经商监管不严", font="SimHei", font_size=36, color="#FF8C00")
        issue3_text.move_to([-3, 0, 0])
        issue3_text.set_opacity(0)
        
        # 创建说明文字
        desc3_text = Text("隐蔽关联、监督盲区", font="SimHei", font_size=28, color="#444444")
        desc3_text.move_to([-3, -1, 0])
        desc3_text.set_opacity(0)
        
        # 确保元素不重叠
        LayoutManager.ensure_screen_bounds(relationship_network)
        LayoutManager.ensure_screen_bounds(issue3_text)
        LayoutManager.ensure_screen_bounds(desc3_text)
        
        # 第四阶段动画
        self.play(
            FadeOut(broken_seal),
            FadeOut(issue2_text),
            FadeOut(desc2_text),
            run_time=0.5
        )
        
        self.play(
            FadeIn(relationship_network, rate_func=smooth),
            run_time=0.8
        )
        
        # 关系网动态效果
        self.play(
            relationship_network.animate.scale(1.02).scale(1/1.02),
            run_time=0.5
        )
        
        self.play(
            FadeIn(issue3_text, rate_func=smooth),
            run_time=0.7
        )
        
        self.play(
            FadeIn(desc3_text, rate_func=smooth),
            run_time=0.5
        )
        
        self.wait(0.5)
        
        # 第五阶段 (11s-14s): 建立完善监督机制
        # 创建缩小版的问题图形
        small_empty_form = ImageMobject(empty_form_path)
        small_empty_form.height = 1.5
        small_empty_form.width = 2  # 宽高比1.33:1
        small_empty_form.move_to([-5, -3, 0])
        small_empty_form.set_opacity(0)
        
        small_broken_seal = ImageMobject(broken_seal_path)
        small_broken_seal.height = 1.5
        small_broken_seal.width = 2  # 宽高比1.33:1
        small_broken_seal.move_to([0, -3, 0])
        small_broken_seal.set_opacity(0)
        
        small_relationship_network = ImageMobject(relationship_network_path)
        small_relationship_network.height = 1.5
        small_relationship_network.width = 2  # 宽高比1.33:1
        small_relationship_network.move_to([5, -3, 0])
        small_relationship_network.set_opacity(0)
        
        # 创建完善监督机制图形
        supervision_mechanism = ImageMobject(supervision_mechanism_path)
        supervision_mechanism.height = 3.5
        supervision_mechanism.width = 6  # 宽高比1.78:1
        supervision_mechanism.move_to([0, 0, 0])
        supervision_mechanism.set_opacity(0)
        
        # 创建关键词文字
        solution1_text = Text("建立完善财产公示与监督机制", font="SimHei", font_size=36, color="#008000")
        solution1_text.move_to([0, 2, 0])
        solution1_text.set_opacity(0)
        
        # 创建说明文字
        desc4_text = Text("透明化、系统化、常态化", font="SimHei", font_size=28, color="#444444")
        desc4_text.move_to([0, 1.5, 0])
        desc4_text.set_opacity(0)
        
        # 确保元素不重叠
        LayoutManager.ensure_screen_bounds(small_empty_form)
        LayoutManager.ensure_screen_bounds(small_broken_seal)
        LayoutManager.ensure_screen_bounds(small_relationship_network)
        LayoutManager.ensure_screen_bounds(supervision_mechanism)
        LayoutManager.ensure_screen_bounds(solution1_text)
        LayoutManager.ensure_screen_bounds(desc4_text)
        
        # 第五阶段动画
        self.play(
            FadeOut(relationship_network),
            FadeOut(issue3_text),
            FadeOut(desc3_text),
            run_time=0.5
        )
        
        # 三个问题图形缩小并移至底部
        self.play(
            FadeIn(small_empty_form),
            FadeIn(small_broken_seal),
            FadeIn(small_relationship_network),
            run_time=0.8
        )
        
        # 完善监督机制图形从上方滑入
        self.play(
            FadeIn(supervision_mechanism, rate_func=smooth),
            supervision_mechanism.animate.scale(1.05).scale(1/1.05),  # 脉动效果
            run_time=1.0
        )
        
        self.play(
            FadeIn(solution1_text, rate_func=smooth),
            run_time=0.7
        )
        
        self.play(
            FadeIn(desc4_text, rate_func=smooth),
            run_time=0.5
        )
        
        self.wait(0.5)
        
        # 第六阶段 (14s-17s): 防止辞职式腐败
        # 创建防止腐败图形
        anti_corruption = ImageMobject(anti_corruption_path)
        anti_corruption.height = 3
        anti_corruption.width = 4  # 宽高比1.33:1
        anti_corruption.move_to([3, 0, 0])
        anti_corruption.set_opacity(0)
        
        # 创建关键词文字
        solution2_text = Text("防止"辞职式腐败"", font="SimHei", font_size=36, color="#008000")
        solution2_text.move_to([3, 2, 0])
        solution2_text.set_opacity(0)
        
        # 创建结论文字
        conclusion_text = Text("强化全过程监管", font="SimHei", font_size=28, color="#00008B")
        conclusion_text.move_to([0, -3, 0])
        conclusion_text.set_opacity(0)
        
        # 确保元素不重叠
        LayoutManager.ensure_screen_bounds(anti_corruption)
        LayoutManager.ensure_screen_bounds(solution2_text)
        LayoutManager.ensure_screen_bounds(conclusion_text)
        
        # 第六阶段动画
        self.play(
            supervision_mechanism.animate.move_to([-3, 0, 0]),
            FadeOut(small_empty_form),
            FadeOut(small_broken_seal),
            FadeOut(small_relationship_network),
            FadeOut(solution1_text),
            FadeOut(desc4_text),
            run_time=0.8
        )
        
        self.play(
            FadeIn(anti_corruption, rate_func=smooth),
            run_time=0.8
        )
        
        # 防止腐败图形效果
        self.play(
            anti_corruption.animate.scale(1.1).scale(1/1.1),
            run_time=0.5
        )
        
        self.play(
            FadeIn(solution2_text, rate_func=smooth),
            run_time=0.7
        )
        
        self.play(
            FadeIn(conclusion_text, rate_func=smooth),
            run_time=0.7
        )
        
        self.wait(1.0)
        
        # 结束动画，所有元素淡出
        self.play(
            FadeOut(supervision_mechanism),
            FadeOut(anti_corruption),
            FadeOut(solution2_text),
            FadeOut(conclusion_text),
            run_time=1.0
        )