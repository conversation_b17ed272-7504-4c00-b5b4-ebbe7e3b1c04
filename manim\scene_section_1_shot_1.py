from manim import *
import os
from layout_manager import LayoutManager

class YellowEarringIncidentAnimation(Scene):
    def construct(self):
        # 设置背景色为白色
        self.camera.background_color = "#FFFFFF"
        
        # 创建占位图像（由于未提供实际路径，使用占位图像）
        # 在实际使用时，请替换为正确的图像路径
        
        # 第一阶段 (0s-5s): 核心简述
        # 创建文字元素
        title = Text("核心简述", font="SimHei", font_size=32, color=BLACK)
        title.to_edge(UP, buff=0.5)
        title.set_opacity(0)
        
        subtitle = Text("黄杨钿甜耳环事件始末", font="SimHei", font_size=24, color=BLACK)
        subtitle.next_to(title, DOWN, buff=0.3)
        subtitle.set_opacity(0)
        
        # 创建耳环素材占位图
        earring_image = Circle(radius=1.5, color=GREEN_E, fill_opacity=0.5)
        earring_image.set_stroke(color=GREEN, width=3)
        earring_image.move_to(ORIGIN)
        earring_image.set_opacity(0)
        
        price_text = Text("230万元", font="SimHei", font_size=28, color=RED)
        price_text.next_to(earring_image, RIGHT, buff=0.5)
        price_text.set_opacity(0)
        
        date_text = Text("2025年5月", font="SimHei", font_size=20, color=BLACK)
        date_text.to_edge(DOWN, buff=0.5)
        date_text.set_opacity(0)
        
        # 确保元素不重叠
        LayoutManager.ensure_screen_bounds(title)
        LayoutManager.ensure_screen_bounds(subtitle)
        LayoutManager.ensure_screen_bounds(earring_image)
        LayoutManager.ensure_screen_bounds(price_text)
        LayoutManager.ensure_screen_bounds(date_text)
        
        # 第一阶段动画
        self.play(
            FadeIn(title, scale=1.2),
            run_time=1
        )
        self.play(
            FadeIn(subtitle),
            run_time=0.8
        )
        self.play(
            FadeIn(earring_image),
            run_time=1
        )
        self.play(
            FadeIn(price_text, scale=1.2),
            run_time=1
        )
        self.play(
            FadeIn(date_text),
            run_time=0.7
        )
        self.wait(0.5)
        
        # 第二阶段 (5s-12s): 网络热议
        # 淡出第一阶段元素
        self.play(
            FadeOut(title),
            FadeOut(subtitle),
            FadeOut(earring_image),
            FadeOut(price_text),
            FadeOut(date_text),
            run_time=1
        )
        
        # 创建网络热议元素
        online_discussion_title = Text("网络热议", font="SimHei", font_size=24, color=BLUE)
        online_discussion_title.to_corner(UL, buff=1)
        online_discussion_title.set_opacity(0)
        
        # 创建网络讨论图标占位
        discussion_icon = VGroup(
            Circle(radius=1, color=BLUE, fill_opacity=0.2),
            Polygon([-0.5, -0.5, 0], [-0.8, -1, 0], [-0.2, -0.7, 0], color=BLUE, fill_opacity=0.5)
        )
        discussion_icon.move_to(LEFT * 4)
        discussion_icon.set_opacity(0)
        
        # 创建网络热议素材占位
        news_image = Rectangle(height=2.65, width=2, color=GREY, fill_opacity=0.3)
        news_image.move_to(RIGHT * 3)
        news_image.set_opacity(0)
        
        earring_attention_text = Text("耳环引发关注", font="SimHei", font_size=20, color=BLACK)
        earring_attention_text.move_to(UP * 2)
        earring_attention_text.set_opacity(0)
        
        father_text = Text("杨伟(前雅安市公务员)", font="SimHei", font_size=22, color=BLACK, weight=BOLD)
        father_text.move_to(RIGHT * 5)
        father_text.set_opacity(0)
        
        # 确保元素不重叠
        LayoutManager.ensure_screen_bounds(online_discussion_title)
        LayoutManager.ensure_screen_bounds(discussion_icon)
        LayoutManager.ensure_screen_bounds(news_image)
        LayoutManager.ensure_screen_bounds(earring_attention_text)
        LayoutManager.ensure_screen_bounds(father_text)
        
        # 第二阶段动画
        self.play(
            FadeIn(online_discussion_title),
            FadeIn(discussion_icon),
            run_time=1
        )
        self.play(
            FadeIn(earring_attention_text),
            run_time=1
        )
        self.play(
            FadeIn(news_image),
            run_time=1
        )
        self.play(
            FadeIn(father_text, scale=1.1),
            run_time=1.5
        )
        self.wait(2.5)
        
        # 第三阶段 (12s-18s): 官方调查
        # 淡出第二阶段元素
        self.play(
            FadeOut(online_discussion_title),
            FadeOut(discussion_icon),
            FadeOut(news_image),
            FadeOut(earring_attention_text),
            FadeOut(father_text),
            run_time=1
        )
        
        # 创建官方调查元素
        official_investigation_title = Text("官方调查", font="SimHei", font_size=24, color=YELLOW)
        official_investigation_title.to_corner(UL, buff=1)
        official_investigation_title.set_opacity(0)
        
        # 创建官方调查图标占位
        official_icon = VGroup(
            Circle(radius=1, color=YELLOW, fill_opacity=0.2),
            Polygon([0, 0.5, 0], [0.5, 0, 0], [0, -0.5, 0], [-0.5, 0, 0], color=YELLOW, fill_opacity=0.5)
        )
        official_icon.move_to(LEFT * 4)
        official_icon.set_opacity(0)
        
        # 创建文件图标素材占位
        document_icon = VGroup(
            Rectangle(height=2, width=1.5, color=GREY, fill_opacity=0.3),
            Line([-0.5, 1, 0], [0.5, 1, 0], color=GREY),
            Line([-0.5, 0.5, 0], [0.5, 0.5, 0], color=GREY),
            Line([-0.5, 0, 0], [0.5, 0, 0], color=GREY),
            Line([-0.5, -0.5, 0], [0.5, -0.5, 0], color=GREY)
        )
        document_icon.move_to(ORIGIN)
        document_icon.set_opacity(0)
        
        violation_text = Text("违规经商及生育问题", font="SimHei", font_size=22, color=RED)
        violation_text.move_to(ORIGIN)
        violation_text.set_opacity(0)
        
        confirmed_text = Text("已确认", font="SimHei", font_size=20, color=GREEN)
        confirmed_text.move_to(RIGHT * 3)
        confirmed_text.set_opacity(0)
        
        # 确保元素不重叠
        LayoutManager.ensure_screen_bounds(official_investigation_title)
        LayoutManager.ensure_screen_bounds(official_icon)
        LayoutManager.ensure_screen_bounds(document_icon)
        LayoutManager.ensure_screen_bounds(violation_text)
        LayoutManager.ensure_screen_bounds(confirmed_text)
        
        # 第三阶段动画
        self.play(
            FadeIn(official_investigation_title),
            FadeIn(official_icon),
            run_time=1
        )
        self.play(
            FadeIn(document_icon),
            run_time=1
        )
        self.play(
            FadeIn(violation_text, scale=1.1),
            run_time=1
        )
        self.play(
            FadeIn(confirmed_text),
            run_time=1
        )
        self.wait(2)
        
        # 第四阶段 (18s-25s): 未解问题和公众讨论
        # 淡出第三阶段元素
        self.play(
            FadeOut(official_investigation_title),
            FadeOut(official_icon),
            FadeOut(document_icon),
            FadeOut(violation_text),
            FadeOut(confirmed_text),
            run_time=1
        )
        
        # 创建问号图标占位
        question_mark = Text("?", font_size=72, color=GREY)
        question_mark.move_to(ORIGIN)
        question_mark.set_opacity(0)
        
        # 创建未解问题文本
        wealth_source_text = Text("家族巨额财富来源", font="SimHei", font_size=22, color=YELLOW)
        wealth_source_text.move_to(LEFT * 4)
        wealth_source_text.set_opacity(0)
        
        earring_authenticity_text = Text("耳环真伪", font="SimHei", font_size=22, color=YELLOW)
        earring_authenticity_text.move_to(RIGHT * 4)
        earring_authenticity_text.set_opacity(0)
        
        no_answer_text = Text("尚无明确答案", font="SimHei", font_size=24, color=RED, slant=ITALIC)
        no_answer_text.next_to(question_mark, DOWN, buff=0.5)
        no_answer_text.set_opacity(0)
        
        # 创建公众讨论图标占位
        public_discussion_icon = Rectangle(height=1.4, width=2.5, color=BLUE_E, fill_opacity=0.2)
        public_discussion_icon.move_to(DOWN * 2)
        public_discussion_icon.set_opacity(0)
        
        public_discussion_text = Text("公众对公职人员财产监督的讨论持续发酵", font="SimHei", font_size=20, color=BLACK)
        public_discussion_text.next_to(public_discussion_icon, DOWN, buff=0.5)
        public_discussion_text.set_opacity(0)
        
        to_be_continued_text = Text("未完待续...", font="SimHei", font_size=24, color=BLACK, slant=ITALIC)
        to_be_continued_text.to_edge(DOWN, buff=0.5)
        to_be_continued_text.set_opacity(0)
        
        # 确保元素不重叠
        LayoutManager.ensure_screen_bounds(question_mark)
        LayoutManager.ensure_screen_bounds(wealth_source_text)
        LayoutManager.ensure_screen_bounds(earring_authenticity_text)
        LayoutManager.ensure_screen_bounds(no_answer_text)
        LayoutManager.ensure_screen_bounds(public_discussion_icon)
        LayoutManager.ensure_screen_bounds(public_discussion_text)
        LayoutManager.ensure_screen_bounds(to_be_continued_text)
        
        # 第四阶段动画
        self.play(
            FadeIn(question_mark, scale=1.2),
            run_time=1
        )
        self.play(
            FadeIn(wealth_source_text),
            FadeIn(earring_authenticity_text),
            run_time=1.5
        )
        
        # 添加虚线连接问题到问号
        line1 = DashedLine(wealth_source_text.get_right(), question_mark.get_left(), dash_length=0.1)
        line2 = DashedLine(earring_authenticity_text.get_left(), question_mark.get_right(), dash_length=0.1)
        
        self.play(
            Create(line1),
            Create(line2),
            run_time=1
        )
        
        self.play(
            FadeIn(no_answer_text),
            run_time=1
        )
        self.play(
            FadeIn(public_discussion_icon),
            FadeIn(public_discussion_text),
            run_time=1.5
        )
        self.play(
            FadeIn(to_be_continued_text),
            run_time=1
        )
        
        # 添加问号轻微跳动动画
        self.play(
            question_mark.animate.scale(1.1),
            run_time=0.5
        )
        self.play(
            question_mark.animate.scale(1/1.1),
            run_time=0.5
        )
        
        # 添加公众讨论图标轻微跳动
        self.play(
            public_discussion_icon.animate.scale(1.1),
            run_time=0.5
        )
        self.play(
            public_discussion_icon.animate.scale(1/1.1),
            run_time=0.5
        )
        
        self.wait(1)